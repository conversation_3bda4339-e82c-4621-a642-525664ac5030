'use client';
import React, { memo } from 'react';
import { Modal } from '@components';
import styles from '@styles/castingcall.module.scss';
import Carousel from '../../Carousel/Carousel';

const ModalAttachmentGallery = ({ onClose, files, startIndex }) => {
  return (
    <Modal
      backdropClose
      onClose={onClose}
      showCloseButton={false}
      showDefaultLayout={false}
      classNameContainer={styles['gallery-modal']}
      classNameContent={styles['gallery-modal-content']}
      containerClose
    >
      <Carousel
        enableArrowNavigation
        startIndex={startIndex}
        className="carousel-attachment-gallery"
        draggable={false}
        loop
      >
        {files
          .filter(({ content_type }) => content_type.split('/')[0] === 'image')
          .map(({ path }, index) => (
            <img key={index} src={path} style={{ width: '100%' }} alt={index} />
          ))}
      </Carousel>
    </Modal>
  );
};

export default memo(ModalAttachmentGallery);
