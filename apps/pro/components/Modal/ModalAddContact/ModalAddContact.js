'use client';
import React, { memo, useEffect, useState } from 'react';
import Modal from '../Modal';
import styles from './ModalAddContact.module.scss';
import {
  Loading,
  ModalAddGroup,
  ModalRemoveContact,
  ModalRemoveContactFromGroup,
  Switch,
} from '@components';
import Api from '@services/api';
import { useAuth } from '@contexts/AuthContext';
import { useNotifications } from '@contexts/NotificationContext';
import { Amp } from '@services/amp';
import { GTM_ACTIONS, GTM_CATEGORIES, GTM_EVENTS } from '@constants/analytics';
import { useAnalytics } from 'use-analytics';
import cn from 'classnames';
import { useContacts } from '@contexts/ContactContext';
import { ErrorMessage } from '@constants/form';

const ModalAddContact = ({
  id,
  imageSrc,
  gender,
  firstName,
  lastName,
  onClose = () => {},
  isContactsPage = false,
  amplitudeScope = Amp.element.scope.global,
}) => {
  const [loading, setLoading] = useState(true);
  const [contactDetails, setContactDetails] = useState({
    isContact: false,
    groupIds: [],
  });
  const [showModalRemoveContactFromGroup, setShowModalRemoveContactFromGroup] =
    useState(false);
  const [showModalRemoveContact, setShowModalRemoveContact] = useState(false);
  const [showModalAddGroup, setShowModalAddGroup] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState(null);

  const {
    groups,
    refreshGroups,
    removeFromGroup,
    addContact,
    removeContact,
    addGroup,
    addToGroup,
  } = useContacts();
  const { profileId } = useAuth();
  const { setNotification } = useNotifications();
  const { track } = useAnalytics();

  useEffect(() => {
    const getContactDetails = async () => {
      const { is_contact, status, contact_group_ids } = await Api.clientside(
        `/profiles/${profileId}/contacts/${id}/info`,
      );

      if (status === 'ok') {
        setContactDetails({
          isContact: is_contact,
          groupIds: contact_group_ids,
        });
      }

      if (!isContactsPage) {
        await refreshGroups();
      }

      setLoading(false);
    };

    getContactDetails().catch();
  }, []);

  const onAddToContactsChange = async (event) => {
    const value = event.target.checked;

    if (value) {
      await addToContacts();
    } else {
      toggleShowModalRemoveContact(id);
    }
  };

  const onAddToGroupChange = async (event, groupId) => {
    const value = event.target.checked;

    if (value) {
      await onAddToGroup(groupId);
    } else {
      toggleShowModalRemoveContactFromGroup(groupId);
    }
  };

  const toggleShowModalRemoveContactFromGroup = (groupId) => {
    setSelectedGroupId(groupId || null);
    setShowModalRemoveContactFromGroup(!showModalRemoveContactFromGroup);
  };

  const toggleShowModalRemoveContact = () => {
    setShowModalRemoveContact(!showModalRemoveContact);
  };

  const toggleShowModalAddGroup = () => {
    setShowModalAddGroup(!showModalAddGroup);
  };

  const addToContacts = async () => {
    setLoading(true);

    Amp.track(Amp.events.elementClicked, {
      name: 'add to contacts',
      scope: Amp.element.scope.global,
      section: Amp.element.section.contactsPopup,
      type: Amp.element.type.toggle,
    });
    track(GTM_EVENTS.interaction, {
      target: GTM_CATEGORIES.b2b,
      action: GTM_ACTIONS.addContacts,
    });

    const body = new FormData();

    body.append('profile', id);

    const response = await Api.clientside(`/profiles/${profileId}/contacts`, {
      method: 'POST',
      body,
    });

    if (response.status !== 'ok') {
      setNotification({
        type: 'error',
        timeout: '5000',
        message: response.message || ErrorMessage.Unexpected,
      });
    } else {
      setContactDetails({
        ...contactDetails,
        isContact: true,
      });

      if (isContactsPage) {
        addContact(id);
      }
    }

    setLoading(false);
  };

  const onAddToGroup = async (groupId, newGroup) => {
    setLoading(true);

    Amp.track(Amp.events.elementClicked, {
      name: 'add to contact group',
      scope: Amp.element.scope.global,
      section: Amp.element.section.contactsPopup,
      type: Amp.element.type.toggle,
    });

    track(GTM_EVENTS.interaction, {
      target: GTM_CATEGORIES.b2b,
      action: GTM_ACTIONS.addContacts,
    });

    const body = new FormData();

    body.append('profiles[]', id);

    const response = await Api.clientside(
      `/contact-groups/${groupId}/contacts`,
      {
        method: 'POST',
        body,
      },
    );

    if (response.status !== 'ok') {
      setNotification({
        type: 'error',
        timeout: '5000',
        message: response.message || ErrorMessage.Unexpected,
      });

      if (newGroup) {
        addGroup(newGroup);
      }
    } else {
      setContactDetails({
        isContact: true,
        groupIds: [...contactDetails.groupIds, groupId],
      });

      if (newGroup) {
        addGroup({ ...newGroup, total: 1 });
      } else {
        addToGroup(groupId, id);
      }
    }

    setLoading(false);
  };

  const onRemoveFromGroup = async () => {
    setLoading(true);
    setShowModalRemoveContactFromGroup(false);

    Amp.track(Amp.events.elementClicked, {
      name: 'remove from contact group',
      scope: amplitudeScope,
      section: Amp.element.section.contactsPopup,
      type: Amp.element.type.button,
    });

    const response = await Api.clientside(
      `/contact-groups/${selectedGroupId}/contacts?profiles[]=${id}`,
      {
        method: 'DELETE',
      },
    );

    if (response.status !== 'ok') {
      setNotification({
        type: 'error',
        timeout: '5000',
        message: response.message || ErrorMessage.Unexpected,
      });
    } else {
      setContactDetails({
        ...contactDetails,
        groupIds: contactDetails.groupIds.filter(
          (groupId) => groupId !== selectedGroupId,
        ),
      });

      if (isContactsPage) {
        removeFromGroup(selectedGroupId, id);
      }
    }

    setLoading(false);
  };

  const onRemove = async () => {
    setLoading(true);
    setShowModalRemoveContact(false);

    Amp.track(Amp.events.elementClicked, {
      name: 'remove from contacts',
      scope: amplitudeScope,
      section: Amp.element.section.contactsPopup,
      type: Amp.element.type.button,
    });

    const response = await Api.clientside(
      `/profiles/${profileId}/contacts?profile=${id}`,
      {
        method: 'DELETE',
      },
    );

    if (response.status !== 'removed') {
      setNotification({
        type: 'error',
        timeout: '5000',
        message: response.message || ErrorMessage.Unexpected,
      });
    } else {
      setContactDetails({
        isContact: false,
        groupIds: [],
      });

      if (isContactsPage) {
        removeContact(id);
      }
    }

    setLoading(false);
  };

  return (
    <>
      <Modal
        onClose={onClose}
        backdropClose={!loading}
        showDefaultLayout={false}
        classNameContainer={cn(styles['modal-container'], {
          [styles.loading]: loading,
        })}
        floatCloseButton
      >
        <div className={styles.container}>
          {loading && (
            <div className={styles['loading-overlay']}>
              <Loading />
            </div>
          )}
          <div className={styles.header}>
            <div>
              <div className={styles['image-container']}>
                <img
                  className={styles.image}
                  src={
                    imageSrc ||
                    `/assets/placeholders/${gender || 'male'}-head.svg`
                  }
                  alt="image"
                />
              </div>
            </div>
            <div className={styles.name}>
              {firstName} {lastName}
            </div>
          </div>
          <div className={styles['switch-container']}>
            <Switch
              label="Add to contacts"
              name="contacts"
              value={contactDetails.isContact}
              onChange={onAddToContactsChange}
            />
          </div>
          {groups.length > 0 && (
            <>
              <div className={styles.title}>Other groups</div>
              <div className={styles['group-container']}>
                {groups.map((group, key) => (
                  <div key={key}>
                    <Switch
                      label={group.title}
                      name={group.id}
                      value={contactDetails.groupIds.some(
                        (groupId) => group.id === groupId,
                      )}
                      onChange={(e) => onAddToGroupChange(e, group.id)}
                    />
                  </div>
                ))}
              </div>
            </>
          )}
          <div
            onClick={() => {
              Amp.track(Amp.events.elementClicked, {
                name: 'add contact group',
                scope: Amp.element.scope.global,
                section: Amp.element.section.contactsPopup,
                type: Amp.element.type.button,
              });
              toggleShowModalAddGroup();
            }}
            className={styles.footer}
          >
            Add to a new group
          </div>
        </div>
      </Modal>
      {showModalRemoveContactFromGroup && (
        <ModalRemoveContactFromGroup
          onClose={toggleShowModalRemoveContactFromGroup}
          onRemoveFromContactsModal={onRemoveFromGroup}
          extendsContactsModal
          contactId={id}
          groupId={selectedGroupId}
        />
      )}
      {showModalRemoveContact && (
        <ModalRemoveContact
          onClose={toggleShowModalRemoveContact}
          onRemoveFromContactsModal={onRemove}
          extendsContactsModal
        />
      )}
      {showModalAddGroup && (
        <ModalAddGroup
          onClose={toggleShowModalAddGroup}
          onAddContactToGroup={onAddToGroup}
          addContactEnabled
        />
      )}
    </>
  );
};

export default memo(ModalAddContact);
