'use client';
import { memo } from 'react';
import styles from './Switch.module.scss';

const Switch = ({ label, onChange, value, name, onClick = () => {} }) => {
  return (
    <div className={styles['switch-container']}>
      <label htmlFor={name} className={styles['switch-label']}>
        {label}
      </label>
      <label className={styles.switch}>
        <input
          className={styles['switch-input']}
          type="checkbox"
          name={name}
          id={name}
          onChange={onChange}
          checked={value}
          onClick={onClick}
        />
        <span className={styles['switch-slider']} />
      </label>
    </div>
  );
};

export default memo(Switch);
