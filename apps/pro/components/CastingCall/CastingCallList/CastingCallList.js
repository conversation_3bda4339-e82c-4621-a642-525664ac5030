'use client';
import React, { Fragment, memo, useState } from 'react';
import {
  Button,
  CastingCallCard,
  CastingCallCardExtended,
  ContactInfo,
  Modal,
} from '@components';
import styles from './CastingCallList.module.scss';
import cn from 'classnames';
import { STATUS, TYPE } from '@constants/castingCalls';
import { useModalContext } from '@contexts/ModalContext';
import Image from 'next/image';
import dayjs from 'dayjs';
import { CastingCallListCta } from '@components/CastingCall/CastingCallList/CastingCallListCta';

const CastingCallList = ({ items = [], isPreview = false }) => {
  const [showContactsModal, setShowContactsModal] = useState(false);
  const { toggleShowPostCastingCallModal } = useModalContext();

  const toggleShowContactsModal = () => {
    setShowContactsModal(!showContactsModal);
  };

  return (
    <>
      {items.length > 0 ? (
        <>
          {!isPreview && (
            <div className={styles['mobile-post-btn']}>
              <Button
                label="Post a casting call"
                minWidth="220px"
                color="green-gradient"
                onClick={toggleShowPostCastingCallModal}
              />
            </div>
          )}
          <div
            className={cn(styles.container, {
              [styles.preview]: isPreview,
            })}
          >
            {items.map((item, index) => (
              <Fragment key={index}>
                {isPreview ? (
                  <CastingCallCard
                    mainCategory={item.category}
                    additionalCategories={item.additional_categories}
                    location={item.location}
                    title={item.title}
                    rolesCount={item.roles.length}
                    expires={item.expiration_date || item.expiration}
                    isOnline={item.online_audition}
                    isEasyToApply={item.type === TYPE.Web}
                    hot={item.hot}
                    id={item.id}
                    status={item.status}
                    slug={item.category.name
                      .toLowerCase()
                      .replace(/[\W_]+/g, '')}
                  />
                ) : (
                  <>
                    <CastingCallCardExtended
                      mainCategory={item.category}
                      additionalCategories={item.additional_categories}
                      location={item.location}
                      title={item.title}
                      description={
                        !isPreview ? item.description.slice(0, 300) : ''
                      }
                      rolesCount={item.roles.length}
                      expires={item.expiration_date || item.expiration}
                      isOnline={item.online_audition}
                      isEasyToApply={item.type === TYPE.Web}
                      hot={item.hot}
                      id={item.id}
                      status={item.status}
                      paymentAmount={item.payment_amount}
                      paymentPeriod={item.payment_period}
                      paymentCurrency={item.payment_currency}
                      candidates={item.candidates}
                      slug={item.category.name
                        .toLowerCase()
                        .replace(/[\W_]+/g, '')}
                      isBoostedDefault={item.is_boost_requested}
                      type={item.type}
                      isExpired={
                        item.status === STATUS.Expired ||
                        dayjs(item.expiration_date || item.expiration).unix() <
                          dayjs().unix()
                      }
                    />
                    {(index === 2 ||
                      (items.length < 4 && index === items.length - 1)) && (
                      <div className={styles['assist-banner']}>
                        <Image
                          src="/assets/icons/icon-contacts-2.svg"
                          alt="icon"
                          width={40}
                          height={40}
                        />
                        <span>
                          Our casting concierge team is here to assist you.{' '}
                          <span
                            onClick={toggleShowContactsModal}
                            className={styles.link}
                          >
                            Contact now
                          </span>
                        </span>
                      </div>
                    )}
                  </>
                )}
              </Fragment>
            ))}
            {!isPreview && (
              <CastingCallListCta onClick={toggleShowPostCastingCallModal} />
            )}
          </div>
        </>
      ) : (
        <div className={styles['not-found']}>
          <span className={styles['not-found-title']}>
            No casting calls found
          </span>
          <span>Please, create a new casting call</span>
          <Button
            label="Post a casting call"
            minWidth="220px"
            color="green-gradient"
            onClick={toggleShowPostCastingCallModal}
          />
        </div>
      )}
      {showContactsModal && (
        <Modal backdropClose onClose={toggleShowContactsModal}>
          <ContactInfo />
        </Modal>
      )}
    </>
  );
};

export default memo(CastingCallList);
