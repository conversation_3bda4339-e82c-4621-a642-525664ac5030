import ctaDesktop from '../../../public/assets/casting-calls/cta-desktop.webp';
import ctaMobile from '../../../public/assets/casting-calls/cta-mobile.webp';
import { getImageProps } from 'next/image';
import styles from './CastingCallList.module.scss';

type CastingCallListCtaProps = {
  onClick: () => void;
};

export const CastingCallListCta = ({ onClick }: CastingCallListCtaProps) => {
  const {
    props: { srcSet: mobileBannerImage, ...mobileBannerRest },
  } = getImageProps({
    alt: 'image',
    src: ctaMobile,
  });

  const {
    props: { srcSet: desktopBannerImage },
  } = getImageProps({
    alt: 'image',
    src: ctaDesktop,
  });

  return (
    <picture>
      <source srcSet={desktopBannerImage} media="(min-width: 480px)" />
      <img onClick={onClick} className={styles.banner} {...mobileBannerRest} />
    </picture>
  );
};
