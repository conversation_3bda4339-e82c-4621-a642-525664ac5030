'use client';
import React, { memo, useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from './MobileSidebarMenu.module.scss';
import { companyLinks, exploreLinks } from '@constants/menuLinks/sidebar-links';
import cn from 'classnames';
import { Button, NumberBadge } from '@components';
import { useRouter } from 'next/navigation';
import { Amp } from '@services/amp';
import { useModalContext } from '@contexts/ModalContext';
import { useAlert } from '@contexts/AlertContext';

const MobileSidebarMenu = ({
  onSideBarClose,
  open,
  isAuthenticated,
  profile,
  authLogout,
}) => {
  const [showSideBar, setShowSideBar] = useState(false);
  const router = useRouter();
  const { toggleShowPostCastingCallModal } = useModalContext();
  const { newReviewCount, newCandidateCount } = useAlert();

  useEffect(() => {
    setShowSideBar(open);
  }, [open]);

  const logout = () => {
    onSideBarClose();
    authLogout();
  };

  const onNavigateToSettings = () => {
    onSideBarClose();
    router.push('/settings');
  };

  const onPostCastingCall = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'post casting call',
      scope: Amp.element.scope.burger,
      section: Amp.element.section.navigation,
      type: Amp.element.type.button,
    });
    onSideBarClose();

    toggleShowPostCastingCallModal();
  };

  return (
    <div
      className={cn(
        styles['sidebar-container'],
        showSideBar && styles['sidebar-open'],
      )}
    >
      <button className={styles['close-btn']} onClick={onSideBarClose}>
        <Image
          width={16}
          height={16}
          src={'/assets/icons/icon-close-1.svg'}
          alt="icon"
        />
      </button>
      <div className={styles['sidebar-header']}>
        <div className={styles['rating-container']}>
          <a
            className={styles.button}
            href={`${profile.profileUrl}/info`}
            onClick={onSideBarClose}
          >
            <img
              className={cn(styles['profile-img'], {
                [styles.placeholder]: !profile.titlePhotoUrl,
              })}
              src={profile.titlePhotoUrl || '/assets/placeholders/director.svg'}
              alt={''}
            />
          </a>
        </div>
        <a
          href={`${profile.profileUrl}`}
          onClick={onSideBarClose}
          className={styles['profile-name']}
        >
          <span>{profile.fullName}</span>
        </a>
        <div className={styles['subscribe-button-container']}>
          <Button
            onClick={onPostCastingCall}
            label="Post casting call"
            color="green-gradient"
            minWidth="200px"
            shadow={false}
          />
          <Button
            label="Contact concierge"
            kind="secondary"
            minWidth="200px"
            color="green-blue"
            type="link"
            href="/contact"
          />
          <Link className={styles.link} href={profile.profileUrl}>
            edit profile
          </Link>
        </div>
      </div>
      <div className={styles.sidebar}>
        <div className={styles.container}>
          <div className={styles.title}>Explore</div>
          <div className={styles['menu-links']}>
            {exploreLinks.map(({ title, id, routerLink }) => (
              <Link
                className={styles['menu-link']}
                key={id}
                href={routerLink}
                onClick={onSideBarClose}
              >
                {title}
                {id === 'reviews' && newReviewCount > 0 && (
                  <NumberBadge number={newReviewCount} />
                )}
                {id === 'submissions' && newCandidateCount > 0 && (
                  <NumberBadge number={newCandidateCount} />
                )}
              </Link>
            ))}
          </div>
        </div>
        <div className={styles.container}>
          <div className={styles.title}>Company</div>
          <div className={styles['menu-links']}>
            {companyLinks.map(({ title, id, routerLink }) => (
              <Link
                className={styles['menu-link']}
                key={id}
                href={routerLink}
                onClick={onSideBarClose}
              >
                {title}
              </Link>
            ))}
          </div>
        </div>
        {isAuthenticated && (
          <div className={styles['action-buttons']}>
            <Button
              className={cn(
                styles['action-button'],
                styles['action-button-logout'],
              )}
              color="grey"
              label="Log Out"
              minWidth="130px"
              shadow={false}
              onClick={logout}
            />
            <Button
              className={cn(
                styles['action-button'],
                styles['action-button-settings'],
              )}
              color="grey"
              label="Settings"
              minWidth="130px"
              shadow={false}
              onClick={onNavigateToSettings}
            />
          </div>
        )}
      </div>
      x
    </div>
  );
};

export default memo(MobileSidebarMenu);
