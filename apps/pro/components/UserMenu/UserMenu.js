'use client';
import React, { memo } from 'react';
import styles from './UserMenu.module.scss';
import IconLogout from '/public/assets/icons/icon-logout.svg';
import IconSettings from '/public/assets/icons/icon-settings.svg';
import IconSubmissions from '/public/assets/icons/icon-speaker.svg';
import Link from 'next/link';
import { useAlert } from '@contexts/AlertContext';
import cn from 'classnames';
import { HelpCenter, NumberBadge } from '@components';
import { usePathname } from 'next/navigation';
import { IconContact, IconMessage } from '../Icon';

const UserMenu = ({
  showHelpCenter,
  toggleHelpCenter,
  authLogout,
  userProfiles,
}) => {
  const path = usePathname();
  const { newConversationCount, newCandidateCount } = useAlert();

  const onLogout = () => {
    authLogout();
  };

  return (
    <section className={styles.sidebar}>
      <div className={styles.content}>
        <div className={styles['menu-section']}>
          <Link
            href="/contacts"
            className={cn(styles.button, {
              [styles.active]:
                path.includes('/contacts') && !path.includes('/settings'),
            })}
          >
            <IconContact className={styles.icon} />
          </Link>
          <Link
            href="/messages"
            className={cn(styles.button, {
              [styles.active]: path.includes('/messages'),
            })}
          >
            <IconMessage className={styles.icon} />
            {newConversationCount > 0 && (
              <NumberBadge
                className={styles.badge}
                number={newConversationCount}
              />
            )}
          </Link>
          <Link
            href="/requests"
            className={cn(styles.button, {
              [styles.active]: path.includes('/requests'),
            })}
          >
            <IconSubmissions />
            {newCandidateCount > 0 && (
              <NumberBadge
                className={styles.badge}
                number={newCandidateCount}
              />
            )}
          </Link>
        </div>
        <div className={styles['menu-section']}>
          {userProfiles?.map(({ profileUrl, titlePhotoUrl }, key) => (
            <a
              href={`${profileUrl}`}
              key={key}
              className={cn(styles.button, {
                [styles.active]: path.includes(profileUrl),
              })}
            >
              <img
                className={cn(styles['profile-img'], {
                  [styles.placeholder]: !titlePhotoUrl,
                })}
                src={titlePhotoUrl || '/assets/placeholders/director.svg'}
                alt="Casting Director"
              />
            </a>
          ))}
          <Link
            href="/settings"
            className={cn(styles.button, {
              [styles.active]: path.includes('/settings'),
            })}
          >
            <IconSettings />
          </Link>
          <div
            className={cn(styles.button, { [styles.active]: showHelpCenter })}
            onClick={toggleHelpCenter}
          >
            ?
          </div>
          <span className={styles.button} onClick={onLogout}>
            <IconLogout />
          </span>
        </div>
      </div>
      <HelpCenter isOpen={showHelpCenter} onClose={toggleHelpCenter} />
    </section>
  );
};

export default memo(UserMenu);
