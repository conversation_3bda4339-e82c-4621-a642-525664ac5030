'use client';
import React, { memo, useState } from 'react';
import cn from 'classnames';
import styles from './HiddenConversations.module.scss';
import IconEnvelope from '../../../public/assets/icons/icon-envelope.svg';

const HiddenConversations = ({
  hiddenNewMessageCount,
  hiddenConversations,
  openProfilePopup,
  openHiddenConversation,
}) => {
  const [showHiddenList, setShowHiddenList] = useState(false);

  const toggleHiddenList = (e) => {
    e.stopPropagation();
    setShowHiddenList(!showHiddenList);
  };

  return (
    <div className={cn(styles['instance'], styles['hidden-instances-tab'])}>
      <div className={styles['tab']} onClick={toggleHiddenList}>
        <IconEnvelope />
        {!!hiddenNewMessageCount && (
          <span className={styles['tab-count']}>
            {hiddenNewMessageCount > 99 ? 99 : hiddenNewMessageCount}
          </span>
        )}
      </div>
      {showHiddenList && (
        <div className={styles['hidden-list']}>
          {hiddenConversations.map((conversation, i) => (
            <div
              className={styles['hidden-item']}
              key={i}
              onClick={() => {
                openHiddenConversation(conversation.conversationId);
              }}
            >
              {conversation.isBatch ? (
                <div className={styles['name']}>{conversation.role.title}</div>
              ) : (
                <>
                  <div
                    className={styles['avatar-container']}
                    onClick={openProfilePopup}
                  >
                    <div className={styles.frame}>
                      <img
                        src={
                          conversation.profile.image ||
                          `/assets/placeholders/circle-${
                            conversation.profile.gender.toLowerCase() || 'male'
                          }-close_up.svg`
                        }
                        className={styles.avatar}
                        alt=""
                      />
                    </div>
                  </div>
                  <div className={styles['name']}>
                    {conversation.profile.firstName}{' '}
                    {conversation.profile.lastName}
                  </div>
                </>
              )}
              {!!conversation.newCount && (
                <span className={styles['tab-count']}>
                  {conversation.newCount > 99 ? 99 : conversation.newCount}
                </span>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default memo(HiddenConversations);
