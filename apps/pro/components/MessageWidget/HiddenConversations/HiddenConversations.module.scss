@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.instance {
  display: none;
  width: 100%;
  position: relative;
  order: 0;
  justify-content: flex-end;

  @include desktop {
    display: flex;
    width: 320px;

    &:first-child {
      display: flex;
    }
  }

  &.is-active {
    z-index: 1;
  }

  @include desktop {
    &.is-hidden {
      display: none;
    }
  }
}

.hidden-instances-tab {
  width: 50px;
  position: relative;
  order: -1;
  z-index: 2;
  display: none;

  @include desktop {
    display: flex;
  }
}

.tab {
  display: flex;
  height: 46px;
  align-items: center;
  box-shadow: $shadow-card-container-light;
  position: relative;
  background: $white;
  width: 50px;
  justify-content: center;
  color: $black-100-opacity-60;
  cursor: pointer;
  border-radius: 10px 10px 0 0;

  &:hover {
    background: $grey-20;
  }
}

.hidden-list {
  background: $white;
  border-radius: 10px;
  position: absolute;
  bottom: calc(100% + 7px);
  left: 50%;
  margin-left: -68px;
  box-shadow: $shadow-card-container-light;
  width: 240px;
  padding: 7px 0 3px;

  &::before {
    content: '';
    position: absolute;
    display: block;
    width: 0;
    left: 68px;
    top: 100%;
    transform: translateX(-50%);
    border: 12px solid transparent;
    border-bottom: 0;
    border-top: 19px solid $white;
  }
}

.hidden-item {
  margin-bottom: 4px;
  display: flex;
  height: 46px;
  padding: 0 $space-10 0 $space-15;
  align-items: center;
  cursor: pointer;

  &:hover {
    background: rgb($black, 0.05);
  }
}

.tab-count {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translate(50%, -50%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $white;
  font-size: 12px;
  font-weight: 700;
  width: 21px;
  height: 21px;
  background: $red-60;
}

.name {
  margin-right: auto;
  line-height: 1.3;
  font-weight: 700;
  font-size: 14px;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  overflow: hidden;
  max-width: 60%;
  position: relative;
  z-index: 2;

  &:not(.batch-name):hover {
    text-decoration: underline;
  }
}

.avatar-container {
  margin-right: $space-15;
}

.frame {
  background: $white;
  border-radius: 50%;
  border: 1px solid $grey-60;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  min-width: 34px;
}

.avatar {
  border-radius: 50%;
  width: 28px;
  height: 28px;
  object-fit: cover;
}
