'use client';
import styles from './MessagePopup.module.scss';
import cn from 'classnames';
import React, { useEffect, useRef, useState } from 'react';
import {
  Loading,
  Modal,
  ModalInviteTalent,
  ModalProfile,
  ModalReportMessage,
  NumberBadge,
} from '@components';
import Messages from '../Messages/Messages';
import Api from '@services/api';
import EmptyConversation from '../EmptyConversation/EmptyConversation';
import { useInterval } from '@utils/useInterval';
import MessageForm from '../../MessageCenter/MessageForm/MessageForm';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import useTabInactivity from '@utils/useTabInactivity';

const MessagePopup = ({
  conversation,
  onClose,
  setActiveConversation,
  toggleCollapseConversation,
  profileId,
}) => {
  const [loading, setLoading] = useState(false);
  const [loadingHistory, setLoadingHistory] = useState(false);
  const [messages, setMessages] = useState([]);
  const [isAllMessagesLoaded, setAllMessagesLoaded] = useState(false);
  const [newMessageCount, setNewMessageCount] = useState(0);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showReportMessage, setShowReportMessage] = useState(null);
  const [selectedImage, setSelectedImage] = useState('');
  const [showInviteTalentModal, setShowInviteTalentModal] = useState(false);

  const ref = useRef();
  const router = useRouter();
  const path = usePathname();

  const isTabInactive = useTabInactivity();

  const MESSAGE_POOLING_INTERVAL = 7000;

  useEffect(() => {
    if (profileId && conversation.conversationId) {
      fetchLatestMessages(conversation.conversationId, true, true).catch();
    }
  }, []);

  useInterval(() => {
    if (profileId && conversation.conversationId && !isTabInactive) {
      fetchLatestMessages(conversation.conversationId, false, false).catch();
    }
  }, MESSAGE_POOLING_INTERVAL);

  const fetchLatestMessages = async (
    conversationId,
    clear = false,
    enableLoader = true,
  ) => {
    if (enableLoader) {
      setLoading(true);
    }

    let lastMessageId;

    if (messages.length > 0 && !clear) {
      if (messages[messages.length - 1]?.id) {
        lastMessageId = messages[messages.length - 1].id;
      } else if (messages[messages.length - 2]?.id) {
        lastMessageId = messages[messages.length - 2].id;
      }
    }

    const start = lastMessageId ? `&start=${lastMessageId}` : '';
    const fetchedMessages =
      (
        await Api.clientside(
          `/profiles/${conversationId}/messages?expand=message-complaints,casting-calls,video-calls${start}`,
        )
      )?.items || [];

    if (lastMessageId) {
      if (fetchedMessages.length > 0) {
        if (lastMessageId === messages[messages.length - 2]?.id) {
          messages.pop();
          setMessages(messages.concat(fetchedMessages));
        } else {
          setMessages(messages.concat(fetchedMessages));
        }
      }
    } else {
      setMessages(fetchedMessages || []);
    }

    if (!enableLoader && fetchedMessages.length) {
      setNewMessageCount(
        fetchedMessages.filter((message) =>
          message.links.author.href.includes(conversation.profile.id),
        ).length,
      );
    }

    setLoading(false);
  };

  const toggleCollapse = () => {
    toggleCollapseConversation(conversation.conversationId);
  };

  const onSetActiveConversation = () => {
    if (!conversation.isActive) {
      setActiveConversation(conversation.conversationId);
    }
  };

  const toggleShowProfileModal = () => {
    setShowProfileModal(!showProfileModal);
  };

  const fetchOlderMessages = async () => {
    if (messages.length === 0 || isAllMessagesLoaded) return;

    setLoadingHistory(true);

    const response = await Api.clientside(
      `/profiles/${conversation.conversationId}/messages?end=${messages[0].id}&expand=message-complaints,casting-calls,video-calls`,
    );

    const fetchedMessages = await response;

    if (fetchedMessages?.items?.length > 0) {
      setMessages(fetchedMessages.items.concat(messages));
    } else {
      setAllMessagesLoaded(true);
    }

    setLoadingHistory(false);
  };

  const closeReportMessageModal = (updatedMessageId) => {
    setShowReportMessage(null);

    if (updatedMessageId) {
      const newMessages = messages.map((message) =>
        message.id === updatedMessageId
          ? { ...message, isReported: true }
          : message,
      );

      setMessages(newMessages);
    }
  };

  const reportMessage = (message) => {
    setShowReportMessage(message);
  };

  const onMessageSuccess = (message) => {
    const newMessages = messages.map((item) => ({
      ...item,
      seen: true,
    }));

    newMessages.push(message);
    setMessages(newMessages);
    ref.current?.setScrollToStart(true);
  };

  const openImageAttachment = (src) => {
    setSelectedImage(src);
  };

  const toggleShowInviteTalentModal = () => {
    setShowInviteTalentModal(!showInviteTalentModal);
  };

  const onInviteSuccess = () => {
    if (path.includes('/requests')) {
      router.push(path);
    }
  };

  return (
    <>
      <div
        className={cn(styles['popup-container'], {
          [styles['is-collapsed']]: conversation.isCollapsed,
        })}
      >
        <div
          className={cn(styles['popup'], {
            [styles['active-window']]: conversation.isActive,
          })}
        >
          <div className={styles['header']}>
            <div
              className={styles['clickable-area']}
              onClick={toggleCollapse}
            />
            <div
              className={styles['avatar-container']}
              onClick={toggleShowProfileModal}
            >
              <div className={styles.frame}>
                <img
                  src={
                    conversation.profile.image ||
                    `/assets/placeholders/circle-${
                      conversation.profile.gender?.toLowerCase() || 'male'
                    }-close_up.svg`
                  }
                  className={styles.avatar}
                  alt=""
                />
              </div>
            </div>
            <div className={styles['name']} onClick={toggleShowProfileModal}>
              {conversation.profile.firstName} {conversation.profile.lastName}
            </div>
            {!!newMessageCount && (
              <NumberBadge
                className={styles['count-badge']}
                color="red"
                number={newMessageCount}
                size={20}
              />
            )}
            <div className={styles['close-icon-container']} onClick={onClose}>
              <Image
                className={styles['close-icon']}
                src="/assets/icons/icon-close-3.svg"
                alt="icon"
                width={15}
                height={15}
              />
            </div>
          </div>
          <div onClick={onSetActiveConversation} className={styles['history']}>
            {!!messages.length && (
              <Messages
                ref={ref}
                messages={messages}
                partner={`${conversation.profile.firstName} ${conversation.profile.lastName}`}
                fetchOlderMessages={fetchOlderMessages}
                reportMessage={reportMessage}
                loadingHistory={loadingHistory}
                profileId={profileId}
              />
            )}
            {loading && <Loading minHeight="100%" />}
            {!loading && !messages.length && (
              <EmptyConversation conversation={conversation} />
            )}
          </div>
          <MessageForm
            conversationId={conversation.profile.id}
            onSetActiveConversation={onSetActiveConversation}
            onMessageSuccess={onMessageSuccess}
            openImageAttachment={openImageAttachment}
            shadowHidden={!loading && !messages.length}
            isWidget
          />
        </div>
      </div>
      {showProfileModal && (
        <ModalProfile
          id={conversation.profile.id}
          onClose={toggleShowProfileModal}
          onInvite={toggleShowInviteTalentModal}
        />
      )}
      {showReportMessage && (
        <ModalReportMessage
          onClose={closeReportMessageModal}
          message={showReportMessage}
          profileId={conversation.profile.id}
        />
      )}
      {selectedImage && (
        <Modal backdropClose onClose={() => setSelectedImage(null)}>
          <img className={styles['image-preview']} src={selectedImage} alt="" />
        </Modal>
      )}
      {showInviteTalentModal && (
        <ModalInviteTalent
          talents={[
            {
              ...conversation.profile,
              titlePhotoUrl: conversation.profile.image,
            },
          ]}
          onClose={toggleShowInviteTalentModal}
          canContactTalent
          onInviteSuccess={onInviteSuccess}
        />
      )}
    </>
  );
};

export default MessagePopup;
