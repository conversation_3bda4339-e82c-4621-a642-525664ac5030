{"name": "talent-allcasting-com", "version": "1.0.0", "private": true, "engines": {"node": ">=20.11.0", "npm": ">=10.2.4"}, "scripts": {"dev": "next dev -p 3100", "build": "next build", "start": "NODE_OPTIONS='-r next-logger' next start", "lint": "next lint", "lint:fix": "next lint --fix", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "lint:all": "npm run prettier:fix & npm run lint:fix", "cypress:open": "cypress open", "cypress:run:dev": "cypress run --config-file cypress.dev.config.js --browser chrome", "cypress:run:test": "cypress run --config-file cypress.stage.config.js --browser chrome", "cypress:run:prod": "cypress run --config-file cypress.config.js --browser chrome"}, "dependencies": {"@amplitude/analytics-browser": "^2.17.6", "@amplitude/experiment-js-client": "^1.15.6", "@amplitude/experiment-node-server": "^1.10.1", "@analytics/google-tag-manager": "^0.6.0", "@aws-sdk/client-s3": "^3.815.0", "@bprogress/next": "^3.2.12", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@livechat/widget-react": "^1.3.4", "@sentry/nextjs": "^9.22.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "analytics": "^0.8.16", "classnames": "^2.5.1", "compressorjs": "^1.2.1", "cookies-next": "^5.1.0", "dayjs": "^1.11.13", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "formik": "^2.4.6", "js-sha256": "^0.11.0", "next": "^15.3.2", "next-logger": "^5.0.1", "pino": "^9.7.0", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-easy-crop": "^5.4.2", "react-scroll-parallax": "^3.4.5", "react-tiny-popover": "^8.1.6", "sharp": "^0.34.2", "use-analytics": "^1.1.0", "yup": "^1.6.1"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "cypress": "^14.4.0", "eslint": "^9.27.0", "eslint-config-next": "^15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-unused-imports": "^4.1.4", "mini-css-extract-plugin": "^2.9.2", "prettier": "^3.5.3", "sass": "^1.89.0", "stylelint": "^16.19.1", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-webpack-plugin": "^5.0.1"}}