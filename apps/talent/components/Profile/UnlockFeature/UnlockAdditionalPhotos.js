'use client';
import styles from '../ProfileAllPhotos.module.scss';
import {
  Button,
  ElementViewed,
  Modal,
  Tooltip,
  TooltipPhotoAdditional,
} from '@components';
import { Amp } from '@services/amp';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useViewport } from '@utils/useViewport';
import { isInViewPort } from '@utils/isInViewPort';
import useTrackElementActions from '@utils/useTrackElementActions';

const UnlockAdditionalPhotos = ({ genderTitle }) => {
  const [showPremiumModal, setShowPremiumModal] = useState(false);

  const router = useRouter();
  const { width } = useViewport();

  const { onTrackClick } = useTrackElementActions({
    name: 'Unlock additional photos block',
    context: Amp.element.context.ctaBlock,
    autoTrackEnabled: false,
  });

  useEffect(() => {
    if (isInViewPort(width, 'tablet', 'min') && showPremiumModal) {
      toggleShowPremiumModal();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [width]);

  const toggleShowPremiumModal = () => {
    setShowPremiumModal(!showPremiumModal);
  };

  const onClick = () => {
    onTrackClick();

    router.push('/upgrade');
  };

  return (
    <>
      <div className={styles['profile-photo-inner-container']}>
        <div className={styles['profile-photo-overlay']}>
          <div className={styles['locked-feature-container']}>
            <div className={styles['locked-feature-inner-container']}>
              <div className={styles['icon-locked-container']}>
                <div className={styles.desktop}>
                  <Tooltip
                    content={
                      <TooltipPhotoAdditional genderTitle={genderTitle} />
                    }
                    clickable
                    openOnHover
                  >
                    <img
                      className={styles['icon-locked']}
                      src="/assets/icons/icon-locked-feature.svg"
                      alt="icon"
                    />
                  </Tooltip>
                </div>
                <div className={styles.mobile}>
                  <img
                    onClick={toggleShowPremiumModal}
                    className={styles['icon-locked']}
                    src="/assets/icons/icon-locked-feature.svg"
                    alt="icon"
                  />
                </div>
              </div>
              <div className={styles['locked-feature-description-container']}>
                <span className={styles['locked-feature-title']}>
                  Additional Photos
                </span>
                <p className={styles['locked-feature-description']}>
                  Increasing your chances to be discovered
                </p>
              </div>
            </div>
            <ElementViewed
              name="Unlock additional photos block"
              type={Amp.element.type.block}
              context={Amp.element.context.ctaBlock}
            >
              <Button
                className={styles['button-unblock']}
                label="Unlock Feature"
                shadow={false}
                color="solid-blue"
                minWidth="105px"
                onClick={onClick}
              />
            </ElementViewed>
          </div>
        </div>
      </div>
      {showPremiumModal && (
        <Modal
          onClose={toggleShowPremiumModal}
          backdropClose
          classNameOverlay={styles['modal-overlay']}
          classNameContainer={styles['modal-container']}
          showDefaultLayout={false}
          disableBackgroundScroll
        >
          <TooltipPhotoAdditional genderTitle={genderTitle} />
        </Modal>
      )}
    </>
  );
};

export default UnlockAdditionalPhotos;
