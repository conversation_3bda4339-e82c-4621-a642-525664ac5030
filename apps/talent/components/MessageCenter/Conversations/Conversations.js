'use client';
import React from 'react';
import styles from './Conversations.module.scss';
import cn from 'classnames';
import NumberBadge from '../../NumberBadge/NumberBadge';
import ConversationActions from '../ConversationActions/ConversationActions';
import { useMessage } from '@contexts/MessageContext';

const Conversations = () => {
  const { conversation, openConversation, toggleChatbot, conversations } =
    useMessage();

  const initConversation = (id) => {
    toggleChatbot(false);
    openConversation(id);
  };

  return (
    <>
      {conversations?.map((item, index) => (
        <div
          key={index}
          className={cn(styles['conversation-button'], {
            [styles.active]: conversation.id === item.links.profile.id,
            [styles['client-support']]: item.client_support_account,
          })}
        >
          <div
            className={styles.container}
            onClick={() => initConversation(item.links.profile.id)}
          >
            <div className={styles.contact}>
              <div className={styles.frame}>
                <img
                  src={
                    item.links.profile.title_photo_url ||
                    `/assets/placeholders/circle-${
                      item.links?.profile?.gender?.title?.toLowerCase() ||
                      'male'
                    }-close_up.svg`
                  }
                  className={styles.avatar}
                  alt=""
                />
              </div>
              {item.links.profile.firstname} {item.links.profile.lastname}
            </div>
            {item.unread > 0 && (
              <NumberBadge
                className={styles.badge}
                number={item.unread}
                size={22}
              />
            )}
          </div>
          <ConversationActions
            id={item.id}
            partnerProfileId={item.links.profile.id}
          />
        </div>
      ))}
    </>
  );
};

export default Conversations;
