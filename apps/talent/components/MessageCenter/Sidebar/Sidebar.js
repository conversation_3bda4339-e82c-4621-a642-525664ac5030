'use client';
import React, { memo } from 'react';
import cn from 'classnames';
import styles from './Sidebar.module.scss';
import { Button, Conversations } from '@components';
import { useMessage } from '@contexts/MessageContext';

const Sidebar = ({ canViewMessages, isChatbotAvailable }) => {
  const {
    conversations,
    conversationSettings,
    loadingConversations,
    showConversationSelect,
    isChatbotActive,
    loadMoreConversations,
    showAllConversations,
    showUnreadConversations,
    toggleChatbot,
  } = useMessage();

  const { status, limit } = conversationSettings;

  return (
    <div
      className={cn(styles.sidebar, {
        [styles['mobile-active']]: showConversationSelect,
      })}
    >
      <h1 className={styles.title}>Messages</h1>
      {canViewMessages && (
        <div className={styles['filter-block']}>
          <Button
            kind={status ? 'secondary' : 'primary'}
            color={status ? 'blue' : 'solid-blue'}
            className={styles['filter-button']}
            label="All"
            onClick={showAllConversations}
          />
          <Button
            kind={status ? 'primary' : 'secondary'}
            color={status ? 'solid-blue' : 'blue'}
            className={styles['filter-button']}
            label="Unread"
            onClick={showUnreadConversations}
          />
        </div>
      )}
      {!loadingConversations && (
        <>
          {conversations.length > 0 ? (
            <>
              <Conversations />
              {conversations.length === limit && (
                <span
                  className={styles['load-more']}
                  onClick={loadMoreConversations}
                >
                  + Load more
                </span>
              )}
            </>
          ) : (
            <div className={styles['no-messages']}>
              <p>You have no conversations.</p>
              <p>
                Please note that only casting directors can initiate
                conversations.
              </p>
              <p>Apply to casting calls to get noticed now.</p>
            </div>
          )}
          {isChatbotAvailable && (
            <button
              onClick={() => toggleChatbot(true)}
              className={cn(styles['conversation-button'], {
                [styles['active']]: isChatbotActive,
              })}
            >
              <div className={styles.contact}>
                <div className={styles.frame}>
                  <img
                    src="/assets/placeholders/circle-male-close_up.svg"
                    className={styles.avatar}
                    alt=""
                  />
                </div>
                Support Bot
              </div>
            </button>
          )}
        </>
      )}
    </div>
  );
};

export default memo(Sidebar);
