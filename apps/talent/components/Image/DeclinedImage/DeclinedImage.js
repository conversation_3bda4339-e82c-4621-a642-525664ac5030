'use client';
import React, { memo, useEffect, useState } from 'react';
import styles from './DeclinedImage.module.scss';
import { Button, Modal, Tooltip, TooltipPhotoDeclined } from '@components';
import cn from 'classnames';
import { useViewport } from '@utils/useViewport';
import { isInViewPort } from '@utils/isInViewPort';

const DeclinedImage = ({
  src,
  onClick,
  contentSize = 'small', // small | medium | large
  actionLabel = '',
  declineReason = 'Bad Content',
  isTooltip = true,
  openModal = () => {},
}) => {
  const [showDeclinedModal, setShowDeclinedModal] = useState(false);
  const { width } = useViewport();

  useEffect(() => {
    if (isInViewPort(width, 'tablet', 'min') && showDeclinedModal) {
      toggleShowDeclineModal();
    }
  }, [width]);

  const toggleShowDeclineModal = () => {
    setShowDeclinedModal(!showDeclinedModal);
  };

  const getDeclinedImage = (isMobile = false) => {
    return (
      <div className={styles['declined-image-container']}>
        <div
          className={cn(
            styles['declined-image-inner-container'],
            styles[contentSize],
          )}
        >
          <img src={src} alt="image" />
          <div>
            <div className={styles['decline-reason-container']}>
              <div
                className={styles['decline-reason-icon-container']}
                onClick={isMobile ? toggleShowDeclineModal : openModal}
              >
                <img
                  className={styles['icon-blocked']}
                  src="/assets/icons/icon-blocked.svg"
                  alt="icon"
                />
                <img
                  className={styles['icon-hint']}
                  src="/assets/icons/icon-hint-white.svg"
                  alt="icon"
                />
              </div>

              <span>
                Your photo <br /> has been blocked
              </span>
              <Button
                type="button"
                kind="primary"
                color="blue"
                label={actionLabel}
                minWidth={'80px'}
                shadow={false}
                className={styles['action-button']}
                onClick={onClick}
              />
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {isTooltip ? (
        <>
          <div className={styles.desktop}>
            <Tooltip
              content={<TooltipPhotoDeclined declineReason={declineReason} />}
              positions={['right', 'left', 'top', 'bottom']}
              openOnHover
              clickable
            >
              {getDeclinedImage()}
            </Tooltip>
          </div>
          <div className={styles.mobile}>{getDeclinedImage(true)}</div>
        </>
      ) : (
        <>{getDeclinedImage()}</>
      )}
      {showDeclinedModal && (
        <Modal
          onClose={toggleShowDeclineModal}
          backdropClose
          classNameOverlay={styles['modal-overlay']}
          classNameContainer={styles['modal-container']}
          showDefaultLayout={false}
        >
          <TooltipPhotoDeclined
            declineReason={declineReason}
            label="Upload new photo"
            onClick={() => {
              toggleShowDeclineModal();
              onClick();
            }}
          />
        </Modal>
      )}
    </>
  );
};

export default memo(DeclinedImage);
