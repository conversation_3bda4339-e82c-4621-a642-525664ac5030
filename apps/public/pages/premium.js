import styles from '../styles/premium.module.scss';
import { FAQItem, GetPremiumButton, Modal, TermsOfUse } from '../components';
import Lightning from '../public/assets/icons/icon-lightning.svg';
import Camera from '../public/assets/icons/icon-camera.svg';
import Book from '../public/assets/icons/icon-book.svg';
import Crown from '../public/assets/icons/icon-crown.svg';
import { useAuth } from '../contexts/AuthContext';
import { faqItems } from '../constants/premium';
import { MainLayout } from '../components/Layouts';
import React, { useEffect, useState } from 'react';
import cn from 'classnames';
import Seo from '../components/Seo/Seo';
import seoPageProps from '../utils/seoPageProps';
import { sharedServerSideProps } from '../utils/sharedServerSideProps';
import { Amp } from '../services/amp';
import Image from 'next/image';

export const getServerSideProps = async ({ resolvedUrl, req, res }) => {
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      seoPage: await seoPageProps(resolvedUrl),
    },
  };
};
export default function Premium({ seoPage }) {
  const [showTerms, setShowTerms] = useState(false);
  const { isAuthenticated } = useAuth();

  const toggleShowTerms = () => {
    setShowTerms(!showTerms);
  };

  useEffect(() => {
    Amp.track(Amp.events.viewPremium);
  }, []);

  return (
    <MainLayout isPremiumHeaderVisible>
      <Seo
        seoPage={seoPage}
        overrideTitle="Premium Subscription | allcasting"
        overrideDescription="Unlock your full potential with our Premium Subscription. Get exclusive access to advanced features, Instagram boost, and more to elevate your experience."
      />
      <div className={styles['hero-block']}>
        <div className={styles.container}>
          <h1>
            Discover Exclusive Advantages with <span>Premium Subscription</span>
          </h1>
          <p className={styles.price}>
            <span className={styles.currency}>$</span>18
            <span className={styles.period}>/mo</span>
          </p>
          <GetPremiumButton
            label="Get Premium"
            minWidth="260px"
            color="purple"
            shadow
            isAuthenticated={isAuthenticated}
          />
          <p className={styles.cancel}>Cancel Anytime</p>
          <p className={styles.terms}>
            This is a 12-month plan. By proceeding you have read and agreed to
            the{' '}
            <span onClick={toggleShowTerms} className={styles['terms-link']}>
              Terms of Use
            </span>
          </p>
        </div>
      </div>
      <div className={styles.container}>
        <div className={styles['feature-block']}>
          <div className={styles.feature}>
            <div className={styles['feature-icon']}>
              <Lightning />
            </div>
            <span className={styles['feature-title']}>Rating boost</span>
            <p>
              Gain an 3000-point rating boost, increasing your chances for
              casting call invitations
            </p>
          </div>
          <div className={styles.feature}>
            <div className={styles['feature-icon']}>
              <Camera />
            </div>
            <span className={styles['feature-title']}>Instagram story</span>
            <p>
              Exclusive Instagram story feature to showcase your talent and
              profile
            </p>
          </div>
          <div className={styles.feature}>
            <div className={styles['feature-icon']}>
              <Book />
            </div>
            <span className={styles['feature-title']}>Mailing list</span>
            <p>
              Profile included in special mailing lists, presented directly to
              state casting directors
            </p>
          </div>
          <div className={styles.feature}>
            <div className={styles['feature-icon']}>
              <Crown />
            </div>
            <span className={styles['feature-title']}>Priority support</span>
            <p>
              Enjoy lifetime access to priority support for immediate assistance
              and queries
            </p>
          </div>
        </div>
      </div>
      <div className={styles.container}>
        <div className={styles['benefit-block']}>
          <h2>
            Increase Your Chances,
            <br />
            Increase Your Earnings
          </h2>
          <div className={styles['benefit-left']}>
            <div className={styles['benefit-image']}>
              <Image
                height={600}
                width={600}
                src="/assets/premium-subscription/boost-pic.webp"
                alt="Amplify Your Presence"
              />
            </div>
            <div className={styles['benefit-description']}>
              <h3>Amplify Your Presence, Boost Earnings</h3>
              <p>
                Unlock a significant 3000-point increase in your rating,
                significantly improving your visibility and likelihood of being
                selected for top-tier casting calls.
              </p>
            </div>
          </div>
          <div className={styles['benefit-right']}>
            <div className={styles['benefit-image']}>
              <Image
                height={600}
                width={600}
                src="/assets/premium-subscription/story-pic.webp"
                alt="Showcase Your Talent"
              />
            </div>
            <div className={styles['benefit-description']}>
              <h3>Showcase Your Talent</h3>
              <p>
                Benefit from an exclusive Instagram story feature, spotlighting
                your profile and talent to a wide audience, enhancing your
                social media presence and appeal.
              </p>
            </div>
          </div>
          <div className={styles['benefit-left']}>
            <div className={styles['benefit-image']}>
              <Image
                height={600}
                width={600}
                src="/assets/premium-subscription/mailng-pic.webp"
                alt="Premium Profile Exposure"
              />
            </div>
            <div className={styles['benefit-description']}>
              <h3>Premium Profile Exposure</h3>
              <p>
                Have your profile strategically placed in specialized mailing
                lists, ensuring direct presentation to influential casting
                directors in your state, opening doors to high-profile
                opportunities.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className={styles['cta-block']}>
        <div className={styles.container}>
          <h2>Try Premium now</h2>
          <GetPremiumButton
            label="Get Premium for $18/month"
            minWidth="300px"
            color="white"
            isAuthenticated={isAuthenticated}
          />
          <p className={styles.cancel}>Cancel any time</p>
          <p className={cn(styles.terms, styles.light)}>
            This is a 12-month plan. By proceeding you have read and agreed to
            the{' '}
            <span onClick={toggleShowTerms} className={styles['terms-link']}>
              Terms of Use
            </span>
          </p>
        </div>
      </div>
      <div className={styles['faq-block']}>
        <div className={styles.container}>
          <h2>Your questions, answered</h2>
          {faqItems.map(({ title, text }, idx) => (
            <FAQItem key={idx} title={title}>
              {text}
            </FAQItem>
          ))}
        </div>
      </div>
      {showTerms && (
        <Modal backdropClose onClose={toggleShowTerms}>
          <TermsOfUse />
        </Modal>
      )}
    </MainLayout>
  );
}
