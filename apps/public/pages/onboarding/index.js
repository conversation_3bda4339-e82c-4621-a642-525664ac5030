import styles from '../../styles/onboarding.module.scss';
import { Parallax, ParallaxProvider } from 'react-scroll-parallax';
import React, { useEffect, useRef, useState } from 'react';
import IconProfile from '../../public/assets/onboarding/icon-profile.svg';
import IconExplore from '../../public/assets/onboarding/icon-explore.svg';
import IconApply from '../../public/assets/onboarding/icon-apply.svg';
import IconReward from '../../public/assets/onboarding/icon-reward.svg';
import ImageMap from '../../public/assets/onboarding/map.svg';
import PinModeling from '../../public/assets/onboarding/pin-modeling.svg';
import PinModelingMini from '../../public/assets/onboarding/pin-modeling.mini.svg';
import PinActing from '../../public/assets/onboarding/pin-acting.svg';
import PinActingMini from '../../public/assets/onboarding/pin-acting.mini.svg';
import PinPromo from '../../public/assets/onboarding/pin-promo.svg';
import PinPromoMini from '../../public/assets/onboarding/pin-promo.mini.svg';
import PinExtras from '../../public/assets/onboarding/pin-extras.svg';
import PinExtrasMini from '../../public/assets/onboarding/pin-extras.mini.svg';
import cn from 'classnames';
import { Loading, RegistrationForm, Seo } from '../../components';
import { useAuth } from '../../contexts/AuthContext';
import seoPageProps from '../../utils/seoPageProps';
import { MainLayout } from '../../components/Layouts';
import { Amp } from '../../services/amp';
import Image from 'next/image';
import profilePictureImage from '../../public/assets/onboarding/profile-picture.webp';
import ellipseImage from '../../public/assets/onboarding/ellipse.png';
import heroImage from '../../public/assets/onboarding/hero.webp';
import eclipseImage from '../../public/assets/onboarding/eclipse.png';
import emptyPhoneImage from '../../public/assets/onboarding/empty-phone.webp';
import photoUxImage from '../../public/assets/onboarding/photo-ux.webp';
import profileUxImage from '../../public/assets/onboarding/profile-ux.webp';
import viewsImage from '../../public/assets/onboarding/views.webp';
import views2Image from '../../public/assets/onboarding/views2.webp';
import attributes2Image from '../../public/assets/onboarding/attributes2.webp';
import attributes3Image from '../../public/assets/onboarding/attributes3.webp';
import phoneMobile1Image from '../../public/assets/onboarding/phone-mobile-1.webp';
import fullheightImage from '../../public/assets/onboarding/fullheight.png';
import sideviewImage from '../../public/assets/onboarding/sideview.png';
import cameraImage from '../../public/assets/onboarding/camera.png';
import phoneMobile2Image from '../../public/assets/onboarding/phone-mobile-2.webp';
import cardModelingImage from '../../public/assets/onboarding/card-modeling.webp';
import cardPromoImage from '../../public/assets/onboarding/card-promo.webp';
import cardExtrasImage from '../../public/assets/onboarding/card-extras.webp';
import applyPhoneImage from '../../public/assets/onboarding/apply-phone.webp';
import cardActingImage from '../../public/assets/onboarding/card-acting.webp';
import applyCardImage from '../../public/assets/onboarding/apply-card.webp';
import messagesImage from '../../public/assets/onboarding/messages.webp';
import phoneMobile3Image from '../../public/assets/onboarding/phone-mobile-3.webp';
import filterImage from '../../public/assets/onboarding/filter.webp';
import phoneMobile4Image from '../../public/assets/onboarding/phone-mobile-4.webp';
import rewardMobileImage from '../../public/assets/onboarding/reward-mobile.png';

export const getStaticProps = async () => ({
  props: {
    seoPage: {
      ...(await seoPageProps('/', false)), // due to static rendering
      ogImageUrl: `${process.env.baseUrl}/assets/meta/main.webp`,
    },
  },
});

function lerp(start, end, t) {
  return start * (1 - t) + end * t;
}

const Onboarding = ({ seoPage }) => {
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [viewportHeight, setViewportHeight] = useState(812);
  const [viewportWidth, setViewportWidth] = useState(768);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [profilePictureTransitionY, setProfilePictureTransitionY] = useState(0);
  const [profilePictureTransitionX, setProfilePictureTransitionX] = useState(0);
  const [actingCardTransitionY, setActingCardTransitionY] = useState(0);
  const mapRef = useRef(null);
  const uploadBlockRef = useRef(null);
  const profilePhoneBlockRef = useRef(null);
  const profilePhoneBlockContainerRef = useRef(null);
  const exploreBlockRef = useRef(null);
  const profilePictureContainerRef = useRef(null);
  const heroImgRef = useRef(null);
  const profilePhoneImgRef = useRef(null);
  const applyPhoneBlockContainerRef = useRef(null);
  const applyPhoneBlockRef = useRef(null);
  const rewardBlockRef = useRef(null);
  const heroBlockRef = useRef(null);
  const { isAuthenticated } = useAuth();

  const [imageLoaded, setImageLoaded] = useState(0);

  const getProfilePictureTransitionY = (
    heroImage,
    phoneImage,
    profilePicture,
  ) => {
    const height1 = heroImage.getBoundingClientRect().height;
    const top1 = heroImage.offsetTop;
    const { height: height2 } = phoneImage.getBoundingClientRect();
    const { height: height3 } = profilePicture.getBoundingClientRect();
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    let distance;

    if (windowWidth < 768) {
      distance = Math.abs(
        windowHeight * 1.05 - (top1 + height1 / 2 - height3 * 0.75),
      );
    } else {
      distance = Math.abs(
        windowHeight +
          (windowHeight - height2) / 2 -
          (top1 + height1 * 0.4 - height3 / 2),
      );
    }

    return (distance / height3) * 100;
  };

  const getProfilePictureTransitionX = (
    startBlock,
    endBlock,
    transitionBlock,
  ) => {
    const { left: left1, width: width1 } = startBlock.getBoundingClientRect();
    const { left: left2, width: width2 } = endBlock.getBoundingClientRect();
    const { width: width3 } = transitionBlock.getBoundingClientRect();

    const distance =
      left1 + (width1 - width3) * 0.4 - (left2 + (width2 - width3) / 2);

    return (distance / width3) * 100;
  };

  const getActingCardTransitionY = (startBlock, endBlock, containerBlock) => {
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const { height: startBlockHeight } = startBlock.getBoundingClientRect();
    const { height: endBlockHeight } = endBlock.getBoundingClientRect();
    const { width: containerBlockWidth } =
      containerBlock.getBoundingClientRect();
    const transitionBlockAspectRatio = 1.49;

    let distance, transitionBlockHeight;

    if (windowWidth < 768) {
      transitionBlockHeight = (windowWidth * 0.46) / transitionBlockAspectRatio;
      distance = Math.abs(
        windowHeight * 3 +
          (windowHeight * 0.1 - endBlockHeight * 0.1) -
          windowHeight * 2.3,
      );
    } else {
      transitionBlockHeight =
        (containerBlockWidth * 0.19) / transitionBlockAspectRatio;
      distance = Math.abs(
        endBlock.offsetTop +
          endBlockHeight * 0.4 -
          (endBlock.offsetTop +
            endBlockHeight +
            (windowHeight - startBlockHeight) / 2),
      );
    }

    return (distance / transitionBlockHeight) * 100;
  };

  const setProfilePhoneBlockPositions = (
    heroImage,
    phoneImage,
    profilePicture,
    profilePictureContainer,
    profilePhoneContent,
    viewsImage,
  ) => {
    const phoneImageRect = phoneImage.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const windowWidth = window.innerWidth;

    let verticalOffset,
      profileTopPosition,
      phoneContentBottomPosition,
      viewImageBottomPosition;

    if (windowWidth < 768) {
      verticalOffset = windowHeight * 0.1;
      profileTopPosition = verticalOffset + phoneImageRect.height * 0.11;
      phoneContentBottomPosition =
        windowHeight - (verticalOffset + phoneImageRect.height * 0.85);
      viewImageBottomPosition = phoneContentBottomPosition * 0.9;
    } else {
      verticalOffset = (windowHeight - phoneImageRect.height) / 2;
      profileTopPosition = verticalOffset + phoneImageRect.height * 0.11;
      phoneContentBottomPosition =
        verticalOffset + phoneImageRect.height * 0.15;
      viewImageBottomPosition = verticalOffset + phoneImageRect.height * 0.05;
    }

    profilePictureContainer.style.top = `${profileTopPosition}px`;
    profilePhoneContent.style.bottom = `${phoneContentBottomPosition}px`;
    profilePhoneContent.style.height = `${phoneImageRect.height}px`;

    if (viewsImage) {
      viewsImage.forEach((image) => {
        image.style.bottom = `${viewImageBottomPosition}px`;
      });
    }

    if (
      phoneImage.getBoundingClientRect().height &&
      profilePicture.getBoundingClientRect().height
    ) {
      const transitionPercentageY = getProfilePictureTransitionY(
        heroImage,
        phoneImage,
        profilePicture,
      );

      setProfilePictureTransitionY(transitionPercentageY);

      const transitionPercentageX = getProfilePictureTransitionX(
        heroImage,
        phoneImage,
        profilePicture,
      );

      setProfilePictureTransitionX(transitionPercentageX);
    }
  };

  const setApplyPhoneBlockPositions = (
    applyPhoneContent,
    phoneImage,
    cardActingImage,
    exploreBlock,
    applyPhoneBlock,
  ) => {
    const phoneImageRect = phoneImage.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const windowWidth = window.innerWidth;

    let verticalOffset,
      cardActingTopPosition,
      applyContentBottomPosition,
      applyContentHeight;

    if (windowWidth < 768) {
      verticalOffset = windowHeight * 0.1;
      cardActingTopPosition = verticalOffset + phoneImageRect.height * 0.1;
      applyContentBottomPosition =
        windowHeight - (verticalOffset + phoneImageRect.height * 0.9);
      applyContentHeight = phoneImageRect.height * 0.9;
    } else {
      verticalOffset = (windowHeight - phoneImageRect.height) / 2;
      cardActingTopPosition = verticalOffset + phoneImageRect.height * 0.1;
      applyContentBottomPosition =
        verticalOffset + phoneImageRect.height * 0.06;
      applyContentHeight = phoneImageRect.height * 0.94;
    }

    cardActingImage.style.top = `${cardActingTopPosition}px`;
    applyPhoneContent.style.bottom = `${applyContentBottomPosition}px`;
    applyPhoneContent.style.height = `${applyContentHeight}px`;

    const transitionPercentage = getActingCardTransitionY(
      phoneImage,
      exploreBlock,
      applyPhoneBlock,
    );

    setActingCardTransitionY(transitionPercentage);
  };

  useEffect(() => {
    const heroImage = document.getElementById('hero-image');
    const phoneImage = document.getElementById('profile-phone-image');
    const profilePhoneContent = document.getElementById(
      'profile-phone-content',
    );
    const profilePicture = document.getElementById('profile-picture');
    const profilePictureContainer = document.getElementById(
      'profile-picture-container',
    );

    const applyPhoneBlock = document.getElementById('apply-phone');
    const applyPhoneContent = document.getElementById('apply-phone-content');
    const applyPhoneImage = document.getElementById('apply-phone-image');
    const cardActingImage = document.getElementById('card-acting');
    const exploreBlock = document.getElementById('explore-block');

    setViewportHeight(window.innerHeight < 812 ? 812 : window.innerHeight);
    setViewportWidth(window.innerWidth);

    const handleResize = () => {
      setViewportHeight(window.innerHeight < 812 ? 812 : window.innerHeight);
      setViewportWidth(window.innerWidth);

      setProfilePhoneBlockPositions(
        heroImage,
        phoneImage,
        profilePicture,
        profilePictureContainer,
        profilePhoneContent,
      );

      setApplyPhoneBlockPositions(
        applyPhoneContent,
        applyPhoneImage,
        cardActingImage,
        exploreBlock,
        applyPhoneBlock,
      );
    };

    const handleScroll = () => {
      setScrollPosition(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    const heroImage = document.getElementById('hero-image');
    const phoneImage = document.getElementById('profile-phone-image');
    const profilePhoneContent = document.getElementById(
      'profile-phone-content',
    );
    const profilePicture = document.getElementById('profile-picture');
    const profilePictureContainer = document.getElementById(
      'profile-picture-container',
    );
    const viewsImage = document.querySelectorAll('.views-image');

    const applyPhoneBlock = document.getElementById('apply-phone');
    const applyPhoneContent = document.getElementById('apply-phone-content');
    const applyPhoneImage = document.getElementById('apply-phone-image');
    const cardActingImage = document.getElementById('card-acting');
    const exploreBlock = document.getElementById('explore-block');

    setProfilePhoneBlockPositions(
      heroImage,
      phoneImage,
      profilePicture,
      profilePictureContainer,
      profilePhoneContent,
      viewsImage,
    );

    setApplyPhoneBlockPositions(
      applyPhoneContent,
      applyPhoneImage,
      cardActingImage,
      exploreBlock,
      applyPhoneBlock,
    );
  }, [imageLoaded]);

  const handleImageLoad = (event) => {
    const imgElement = event.target;

    setImageLoaded(imgElement.clientHeight);
  };

  const calculateRotation = (
    scrollPosition,
    startScroll,
    middleScroll,
    endScroll,
    startRotation,
    middleRotation,
    endRotation,
  ) => {
    if (scrollPosition >= startScroll && scrollPosition <= middleScroll) {
      const lerpValue =
        (scrollPosition - startScroll) / (middleScroll - startScroll);

      return lerp(startRotation, middleRotation, lerpValue);
    } else if (scrollPosition > middleScroll && scrollPosition <= endScroll) {
      const lerpValue =
        (scrollPosition - middleScroll) / (endScroll - middleScroll);

      return lerp(middleRotation, endRotation, lerpValue);
    } else if (scrollPosition > endScroll) {
      return endRotation;
    }

    return startRotation;
  };

  const applyRotation = () => {
    const rotationAngle = calculateRotation(
      scrollPosition,
      0,
      250,
      516,
      0,
      9,
      0,
    );
    const element = document.getElementById('profile-picture');

    if (element) {
      element.style.transform = `rotate(${rotationAngle}deg)`;
    }
  };

  const calculateTransformValue = (
    scrollPosition,
    startPosition,
    endPosition,
    startValue,
    endValue,
  ) => {
    if (scrollPosition >= startPosition && scrollPosition <= endPosition) {
      const lerpValue =
        (scrollPosition - startPosition) / (endPosition - startPosition);

      return lerp(startValue, endValue, lerpValue);
    } else if (scrollPosition > endPosition) {
      return endValue;
    }

    return startValue;
  };

  const applyMapPointOpacity = () => {
    const startScroll =
      exploreBlockRef.current.offsetTop -
      exploreBlockRef.current.getBoundingClientRect().height * 0.5;
    const endScroll =
      exploreBlockRef.current.offsetTop +
      exploreBlockRef.current.getBoundingClientRect().height * 0.5;
    const startOpacity = 0;
    const endOpacity = 1;

    const opacityLevel = calculateTransformValue(
      scrollPosition,
      startScroll,
      endScroll,
      startOpacity,
      endOpacity,
    );

    if (mapRef.current) {
      const svgElement = mapRef.current.querySelector('svg');
      const circleElements = svgElement.querySelectorAll('circle');

      circleElements.forEach((circle) => {
        circle.style.opacity = opacityLevel;
      });
    }
  };

  const applyCardActingTransition = () => {
    const element = document.getElementById('card-acting');
    const windowWidth = window.innerWidth;
    const startPosition = viewportHeight * 3.1;
    const endPosition = viewportHeight * 4;
    const startScale = 0;
    const endScale = windowWidth < 768 ? 1 : 1.1;

    let transitionX = windowWidth < 768 ? 0 : 85;

    let transitionY = -actingCardTransitionY;

    let rotationAngle = 0;

    if (scrollPosition >= startPosition) {
      transitionX = calculateTransformValue(
        scrollPosition,
        startPosition,
        endPosition,
        windowWidth < 768 ? 0 : 85,
        0,
      );
      transitionY = calculateTransformValue(
        scrollPosition,
        startPosition,
        endPosition,
        -actingCardTransitionY,
        0,
      );
      rotationAngle = calculateRotation(
        scrollPosition,
        startPosition,
        startPosition + (endPosition - startPosition) / 2,
        endPosition,
        0,
        9,
        0,
      );
    }

    const scale = calculateTransformValue(
      scrollPosition,
      viewportHeight * 3,
      viewportHeight * 3.1,
      startScale,
      endScale,
    );

    if (element) {
      element.style.transform = `translate3d(${transitionX}%, ${transitionY}%, 0) scale(${scale}) rotate(${rotationAngle}deg)`;
    }
  };

  const applyCardActingVisibility = () => {
    const startPosition =
      viewportWidth < 768 ? viewportHeight * 4.5 : viewportHeight * 5.2;
    const element = document.getElementById('card-acting');

    if (scrollPosition >= startPosition) {
      if (element) {
        element.style.display = `none`;
      }
    } else {
      if (element) {
        element.style.display = `initial`;
      }
    }
  };

  const applyFilterImageOpacity = () => {
    const startPosition =
      viewportWidth < 768 ? viewportHeight * 4 : viewportHeight * 4.3;
    const endPosition =
      viewportWidth < 768 ? viewportHeight * 4.2 : viewportHeight * 4.5;
    const startOpacity = 1;
    const endOpacity = 0;

    if (scrollPosition >= startPosition) {
      const opacityLevel = calculateTransformValue(
        scrollPosition,
        startPosition,
        endPosition,
        startOpacity,
        endOpacity,
      );

      const element = document.getElementById('filter-image');

      if (element) {
        element.style.opacity = `${opacityLevel}`;
      }
    }
  };

  const applyMessagesImageOpacity = () => {
    const startPosition =
      viewportWidth < 768 ? viewportHeight * 4.8 : viewportHeight * 5.6;
    const endPosition =
      viewportWidth < 768 ? viewportHeight * 5 : viewportHeight * 6;
    const startOpacity = 1;
    const endOpacity = 0;

    if (scrollPosition >= startPosition) {
      const opacityLevel = calculateTransformValue(
        scrollPosition,
        startPosition,
        endPosition,
        startOpacity,
        endOpacity,
      );

      const element = document.getElementById('apply-message-image');

      if (element) {
        element.style.opacity = `${opacityLevel}`;
      }
    }
  };

  const isElementInView = (element) => {
    return element.getBoundingClientRect().top <= window.innerHeight;
  };

  const fixPhonePosition = (phoneBlock, phoneBlockContainer, endBlock) => {
    if (
      phoneBlockContainer.getBoundingClientRect().top <= 0 &&
      !isElementInView(endBlock)
    ) {
      phoneBlock.style.position = 'fixed';
      phoneBlock.style.top = '0';
    } else if (isElementInView(endBlock)) {
      phoneBlock.style.position = 'absolute';
      phoneBlock.style.top =
        endBlock.offsetTop -
        phoneBlockContainer.offsetTop -
        phoneBlock.getBoundingClientRect().height +
        'px';
    } else {
      phoneBlock.style.position = 'absolute';
      phoneBlock.style.top = '0';
    }
  };

  useEffect(() => {
    fixPhonePosition(
      profilePhoneBlockRef.current,
      profilePhoneBlockContainerRef.current,
      exploreBlockRef.current,
    );
    fixPhonePosition(
      applyPhoneBlockRef.current,
      applyPhoneBlockContainerRef.current,
      rewardBlockRef.current,
    );

    if (window.innerWidth > 768) {
      applyRotation();
    }

    if (isElementInView(exploreBlockRef.current)) {
      applyMapPointOpacity();
    }
    applyCardActingTransition();
    applyCardActingVisibility();
    applyFilterImageOpacity();
    applyMessagesImageOpacity();
  }, [scrollPosition]);

  const onRedirecting = async (value) => {
    setIsRedirecting(value);
  };

  useEffect(() => {
    Amp.track(Amp.events.viewOnboarding);
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      <div className={styles['page-wrapper']}>
        <div className={styles['container']}>
          <ParallaxProvider isDisabled={viewportWidth < 769}>
            <Parallax translateY={['0px', '-100px']}>
              <Image
                src={ellipseImage}
                alt={''}
                className={styles['ellipse']}
              />
            </Parallax>
            <div className={styles['hero-block']} ref={heroBlockRef}>
              <div>
                <Seo
                  seoPage={seoPage}
                  overrideH1={'Explore, apply, succeed.'}
                />
                <p>
                  Welcome to AllCasting.com – your platform for creative
                  exploration, connection, and limitless possibilities. Whether
                  you&apos;re an actor, model, musician, dancer, or any kind of
                  performer, this is your space to showcase your talent, find
                  exciting opportunities, and embark on a journey of artistic
                  growth.
                </p>
              </div>
              <div className={styles['block-image']}>
                <Image
                  src={heroImage}
                  alt={''}
                  id="hero-image"
                  ref={heroImgRef}
                  onLoad={handleImageLoad}
                />
              </div>
            </div>
            <div
              className={styles['phone-block-container']}
              ref={profilePhoneBlockContainerRef}
            >
              <div
                className={cn(styles['phone-block'], styles['profile-phone'])}
                id="profile-phone"
                ref={profilePhoneBlockRef}
              >
                <Image
                  src={eclipseImage}
                  alt={''}
                  className={styles['eclipse-image']}
                />
                <Image
                  src={emptyPhoneImage}
                  alt={''}
                  id={'profile-phone-image'}
                  className={styles['phone-image']}
                  ref={profilePhoneImgRef}
                />
                <Parallax
                  translateX={[profilePictureTransitionX, 0]}
                  translateY={[-profilePictureTransitionY, 0]}
                  startScroll={0}
                  endScroll={viewportHeight / 1.3}
                  disabled={viewportWidth < 768}
                  className={styles['profile-picture']}
                  id="profile-picture-container"
                >
                  <div>
                    <Image
                      src={profilePictureImage}
                      alt={''}
                      ref={profilePictureContainerRef}
                      id="profile-picture"
                      onLoad={handleImageLoad}
                    />
                  </div>
                </Parallax>
                <div
                  id="profile-phone-content"
                  className={styles['phone-content']}
                >
                  <Parallax
                    opacity={[0, 1]}
                    startScroll={
                      viewportWidth < 768
                        ? viewportHeight / 1.3
                        : viewportHeight / 1.2
                    }
                    endScroll={
                      viewportWidth < 768
                        ? viewportHeight / 1.2
                        : viewportHeight
                    }
                    className={styles['photo-ux-image']}
                  >
                    <Image src={photoUxImage} alt={''} />
                  </Parallax>
                  <Parallax
                    translateY={[150, 0]}
                    startScroll={
                      viewportWidth < 768
                        ? viewportHeight * 1.5
                        : viewportHeight * 1.3
                    }
                    endScroll={
                      viewportWidth < 768
                        ? viewportHeight * 1.6
                        : viewportHeight * 1.9
                    }
                    className={styles['profile-ux-image']}
                  >
                    <Image src={profileUxImage} alt={''} />
                  </Parallax>
                </div>
                <Parallax
                  opacity={[0, 1]}
                  startScroll={viewportHeight / 1.1}
                  endScroll={viewportHeight / 0.9}
                  className={cn('views-image', styles['views-image'])}
                >
                  <Image src={viewsImage} alt={''} />
                </Parallax>
                <Parallax
                  opacity={[0, 1]}
                  startScroll={viewportHeight * 1.9}
                  endScroll={viewportHeight * 1.95}
                  className={cn('views-image', styles['views-image'])}
                >
                  <Image src={views2Image} alt={''} />
                </Parallax>
                <Parallax
                  opacity={[0, 1]}
                  startScroll={viewportHeight * 1.9}
                  endScroll={viewportHeight * 1.95}
                  className={styles['attributes-image-1']}
                >
                  <img src={'assets/onboarding/attributes1.svg'} alt={''} />
                </Parallax>
                <Parallax
                  opacity={[0, 1]}
                  startScroll={viewportHeight * 1.9}
                  endScroll={viewportHeight * 1.95}
                  className={styles['attributes-image-2']}
                >
                  <Image src={attributes2Image} alt={''} />
                </Parallax>
                <Parallax
                  opacity={[0, 1]}
                  startScroll={viewportHeight * 1.95}
                  endScroll={viewportHeight * 2}
                  className={styles['attributes-image-3']}
                >
                  <Image src={attributes3Image} alt={''} />
                </Parallax>
              </div>
            </div>

            <div
              className={cn(
                styles['description-block'],
                styles['upload-description'],
              )}
              ref={uploadBlockRef}
            >
              <Parallax
                opacity={[0, 1]}
                startScroll={viewportHeight / 1.2}
                endScroll={viewportHeight}
                disabled={viewportWidth < 768}
                className={styles['description-images']}
              >
                <Image
                  src={phoneMobile1Image}
                  className={styles['phone-image-mobile']}
                  alt=""
                />
                <div className={styles['fullheight-image']}>
                  <Image src={fullheightImage} alt={''} />
                </div>
                <div className={styles['sideview-image']}>
                  <Image src={sideviewImage} alt={''} />
                </div>
                <div className={styles['camera-image']}>
                  <Image src={cameraImage} alt={''} />
                </div>
              </Parallax>
              <Parallax
                opacity={[1, 0]}
                startScroll={viewportHeight}
                endScroll={viewportHeight * 1.2}
              >
                <div className={styles['block-title']}>
                  <IconProfile />
                  Start with your profile
                </div>
                <h2>Upload your photo</h2>
                <p>
                  Your profile photo isn&apos;t just a picture – it&apos;s your
                  introduction, your calling card, and the key to capturing the
                  attention of casting professionals.
                </p>
              </Parallax>
            </div>
            <div
              className={cn(
                styles['description-block'],
                styles['upload-description'],
              )}
            >
              <div className={styles['description-images']}>
                <Image
                  src={phoneMobile2Image}
                  className={styles['phone-image-mobile']}
                  alt=""
                />
              </div>
              <Parallax
                opacity={[1, 0]}
                startScroll={viewportHeight * 2}
                endScroll={viewportHeight * 2.2}
              >
                <div className={styles['block-title']}>
                  <IconProfile />
                  Start with your profile
                </div>
                <h2>Fill in profile</h2>
                <p>
                  Navigate to your profile section and start filling in.
                  Remember, the more details you provide, the better your
                  chances of landing exciting opportunities.
                </p>
              </Parallax>
            </div>

            <div
              id="explore-block"
              className={styles['explore-block']}
              ref={exploreBlockRef}
            >
              <div className={styles['explore-block-content']}>
                <div className={styles['block-title']}>
                  <IconExplore />
                  Now you are ready to explore
                </div>
                <h2>100+ Casting Calls are posted weekly</h2>
                <p>Start exploring casting calls near you.</p>

                <div className={styles['map-block']}>
                  <div ref={mapRef}>
                    <ImageMap className={styles['map-image']} />
                  </div>
                  <Parallax
                    scale={[0, 1]}
                    startScroll={viewportHeight * 2.5}
                    endScroll={viewportHeight * 2.9}
                    className={cn(styles['pin-modeling'], styles['pin'])}
                  >
                    <Parallax
                      scale={[1, 0]}
                      startScroll={viewportHeight * 2.9}
                      endScroll={viewportHeight * 3.2}
                    >
                      <PinModeling className={styles['pin-desktop']} />
                      <PinModelingMini className={styles['pin-mobile']} />
                    </Parallax>
                  </Parallax>
                  <Parallax
                    scale={[0, 1]}
                    startScroll={viewportHeight * 2.5}
                    endScroll={viewportHeight * 2.9}
                    className={cn(styles['pin-acting'], styles['pin'])}
                  >
                    <Parallax
                      scale={[1, 0]}
                      startScroll={viewportHeight * 2.9}
                      endScroll={viewportHeight * 3.2}
                    >
                      <PinActing className={styles['pin-desktop']} />
                      <PinActingMini className={styles['pin-mobile']} />
                    </Parallax>
                  </Parallax>
                  <Parallax
                    scale={[0, 1]}
                    startScroll={viewportHeight * 2.5}
                    endScroll={viewportHeight * 2.9}
                    className={cn(styles['pin-promo'], styles['pin'])}
                  >
                    <Parallax
                      scale={[1, 0]}
                      startScroll={viewportHeight * 2.9}
                      endScroll={viewportHeight * 3.2}
                    >
                      <PinPromo className={styles['pin-desktop']} />
                      <PinPromoMini className={styles['pin-mobile']} />
                    </Parallax>
                  </Parallax>
                  <Parallax
                    scale={[0, 1]}
                    startScroll={viewportHeight * 2.5}
                    endScroll={viewportHeight * 2.9}
                    className={cn(styles['pin-extras'], styles['pin'])}
                  >
                    <Parallax
                      scale={[1, 0]}
                      startScroll={viewportHeight * 2.9}
                      endScroll={viewportHeight * 3.2}
                    >
                      <PinExtras className={styles['pin-desktop']} />
                      <PinExtrasMini className={styles['pin-mobile']} />
                    </Parallax>
                  </Parallax>
                  <Parallax
                    scale={[0, 1]}
                    startScroll={viewportHeight * 3}
                    endScroll={viewportHeight * 3.1}
                    className={cn(styles['card-modeling'], styles['card'])}
                  >
                    <Image src={cardModelingImage} alt={''} />
                  </Parallax>
                  <Parallax
                    scale={[0, 1]}
                    startScroll={viewportHeight * 3}
                    endScroll={viewportHeight * 3.1}
                    className={cn(styles['card-promo'], styles['card'])}
                  >
                    <Image src={cardPromoImage} alt={''} />
                  </Parallax>
                  <Parallax
                    scale={[0, 1]}
                    startScroll={viewportHeight * 3}
                    endScroll={viewportHeight * 3.1}
                    className={cn(styles['card-extras'], styles['card'])}
                  >
                    <Image src={cardExtrasImage} alt={''} />
                  </Parallax>
                </div>
              </div>
            </div>

            <div
              className={styles['phone-block-container']}
              ref={applyPhoneBlockContainerRef}
            >
              <div
                className={cn(styles['phone-block'], styles['apply-phone'])}
                id="apply-phone"
                ref={applyPhoneBlockRef}
              >
                <Image
                  src={eclipseImage}
                  alt={''}
                  className={styles['eclipse-image']}
                />
                <Image
                  src={applyPhoneImage}
                  alt={''}
                  id={'apply-phone-image'}
                  className={styles['phone-image']}
                />
                <div id="card-acting" className={styles['card-acting']}>
                  <Image src={cardActingImage} alt={''} />
                </div>

                <div
                  id={'apply-phone-content'}
                  className={styles['phone-content']}
                >
                  <Parallax
                    opacity={[0, 1]}
                    startScroll={
                      viewportWidth < 768
                        ? viewportHeight * 4.5
                        : viewportHeight * 4.8
                    }
                    endScroll={
                      viewportWidth < 768
                        ? viewportHeight * 4.6
                        : viewportHeight * 4.9
                    }
                    className={styles['check-mark-image']}
                  >
                    <img src={'assets/onboarding/check-mark.svg'} alt={''} />
                  </Parallax>
                  <Parallax
                    opacity={[0, 1]}
                    startScroll={
                      viewportWidth < 768
                        ? viewportHeight * 4.5
                        : viewportHeight * 5.2
                    }
                    endScroll={
                      viewportWidth < 768
                        ? viewportHeight * 4.6
                        : viewportHeight * 5.3
                    }
                    id="apply-message-image"
                    className={styles['apply-message-image']}
                  >
                    <img src={'assets/onboarding/apply-message.svg'} alt={''} />
                  </Parallax>
                  <Parallax
                    translateY={[500, 0]}
                    startScroll={
                      viewportWidth < 768
                        ? viewportHeight * 4.1
                        : viewportHeight * 4.4
                    }
                    endScroll={
                      viewportWidth < 768
                        ? viewportHeight * 4.5
                        : viewportHeight * 4.8
                    }
                    className={styles['apply-card-image']}
                  >
                    <Image src={applyCardImage} alt={''} />
                  </Parallax>
                  <Parallax
                    translateY={[500, 0]}
                    startScroll={
                      viewportWidth < 768
                        ? viewportHeight * 4.4
                        : viewportHeight * 5.4
                    }
                    endScroll={
                      viewportWidth < 768
                        ? viewportHeight * 5
                        : viewportHeight * 6
                    }
                    className={styles['messages-image']}
                  >
                    <Image src={messagesImage} alt={''} />
                  </Parallax>
                </div>
              </div>
            </div>
            <div
              className={cn(
                styles['description-block'],
                styles['apply-description'],
              )}
            >
              <div className={styles['description-images']}>
                <Image
                  src={phoneMobile3Image}
                  className={cn(styles['phone-image-mobile'], styles['large'])}
                  alt=""
                />
                <Parallax
                  opacity={[0, 1]}
                  startScroll={
                    viewportWidth < 768
                      ? viewportHeight * 3.8
                      : viewportHeight * 3.9
                  }
                  endScroll={
                    viewportWidth < 768
                      ? viewportHeight * 4
                      : viewportHeight * 4.1
                  }
                  id="filter-image"
                  className={styles['filter-image']}
                >
                  <Image src={filterImage} alt={''} />
                </Parallax>
              </div>
              <Parallax
                opacity={[1, 0]}
                startScroll={
                  viewportWidth < 768 ? viewportHeight * 4 : viewportHeight * 5
                }
                endScroll={
                  viewportWidth < 768
                    ? viewportHeight * 4.2
                    : viewportHeight * 5.2
                }
                className={styles['text-block']}
              >
                <div className={styles['block-title']}>
                  <IconApply />
                  Apply
                </div>
                <h2>Apply to matching casting calls</h2>
                <p>Once you find a casting call that excites you - apply.</p>
              </Parallax>
            </div>
            <div
              className={cn(
                styles['description-block'],
                styles['message-description'],
              )}
            >
              <div className={styles['description-images']}>
                <Image
                  src={phoneMobile4Image}
                  className={cn(styles['phone-image-mobile'], styles['large'])}
                  alt=""
                />
              </div>
              <Parallax
                opacity={[1, 0]}
                startScroll={
                  viewportWidth < 768 ? viewportHeight * 5 : viewportHeight * 6
                }
                endScroll={
                  viewportWidth < 768
                    ? viewportHeight * 5.2
                    : viewportHeight * 6.2
                }
                className={styles['text-block']}
              >
                <div className={styles['block-title']}>
                  <IconApply />
                  Apply
                </div>
                <h2>Communicate with casting directors</h2>
                <p>
                  Highlight your greatest skills and get invited for auditions!
                </p>
              </Parallax>
            </div>
            <div className={styles['reward-block']} ref={rewardBlockRef}>
              <div className={styles['background']}>
                <Image src={rewardMobileImage} alt="" />
              </div>
              <Parallax
                translateY={[-10, 10]}
                translateX={[-10, 0]}
                speed={5}
                className={styles['confetti-blurred']}
              ></Parallax>
              <Parallax
                translateY={[-10, 10]}
                translateX={[-10, 0]}
                speed={10}
                className={styles['confetti-violet']}
              ></Parallax>
              <Parallax
                translateX={[-10, 0]}
                speed={15}
                className={styles['confetti-yellow']}
              ></Parallax>
              <div className={styles['text-content']}>
                <div className={styles['block-title']}>
                  <IconReward />
                  Success!
                </div>
                <h2>Get a reward</h2>
                <p>
                  From acting to modeling, voiceovers to music gigs, each
                  opportunity comes with its own rewards.
                </p>
              </div>
            </div>
            {!isAuthenticated && (
              <div className={styles['registration-form']}>
                <h2>Ready to shine?</h2>
                <p>
                  Sign up now and embark on a journey of growth and
                  transformation. Elevate your skills, boost your confidence,
                  and unlock new opportunities in the world of creativity. Your
                  success story starts here!
                </p>
                <div className={styles['form']}>
                  {isRedirecting ? (
                    <div className={styles.redirecting}>
                      <h1>Authentication</h1>
                      <p>It will take a moment, please stand by.</p>
                      <Loading minHeight="50px" padding="0" />
                    </div>
                  ) : (
                    <>
                      Sign up with
                      <RegistrationForm
                        isTalent
                        onRedirecting={onRedirecting}
                        styleTheme={'dark'}
                      />
                    </>
                  )}
                </div>
              </div>
            )}
          </ParallaxProvider>
        </div>
      </div>
    </MainLayout>
  );
};

export default Onboarding;
