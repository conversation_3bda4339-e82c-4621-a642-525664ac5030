import React, { memo } from 'react';
import styles from './UserMenu.module.scss';
import IconLogout from '/public/assets/icons/icon-logout-1.svg';
import IconMessages from '/public/assets/icons/icon-messages.svg';
import IconSettings from '/public/assets/icons/icon-settings-1.svg';
import { useAlert } from '../../contexts/AlertContext';
import cn from 'classnames';
import { HelpCenter, NumberBadge } from '..';
import Link from 'next/link';

const UserMenu = ({
  showHelpCenter,
  toggleHelpCenter,
  showSale,
  stopSale,
  authLogout,
  userProfiles,
  redirectURL,
}) => {
  const { newConversationCount } = useAlert();

  const onLogout = () => {
    stopSale();
    authLogout();
  };

  return (
    <section
      className={cn(styles.sidebar, {
        [styles['sidebar-sale']]: showSale,
      })}
    >
      <div className={styles.content}>
        <div className={styles['menu-section']}>
          <Link href={`${redirectURL}/messages`} className={styles.button}>
            <IconMessages />
            {newConversationCount > 0 && (
              <NumberBadge
                className={styles.badge}
                number={newConversationCount}
              />
            )}
          </Link>
        </div>
        <div className={styles['menu-section']}>
          {userProfiles?.map(({ profileUrl, titlePhotoUrl, gender }, key) => (
            <Link
              href={`${redirectURL}${profileUrl}/info`}
              key={key}
              className={styles.button}
            >
              <img
                className={styles['profile-img']}
                src={
                  titlePhotoUrl ||
                  `/assets/placeholders/circle-${gender}-headshot.svg`
                }
                alt="Talent"
              />
            </Link>
          ))}
          <Link href={`${redirectURL}/settings`} className={styles.button}>
            <IconSettings />
          </Link>
          <div
            className={cn(styles.button, { [styles.active]: showHelpCenter })}
            onClick={toggleHelpCenter}
          >
            ?
          </div>
          <span className={styles.button} onClick={onLogout}>
            <IconLogout />
          </span>
        </div>
      </div>
      <HelpCenter isOpen={showHelpCenter} onClose={toggleHelpCenter} />
    </section>
  );
};

export default memo(UserMenu);
