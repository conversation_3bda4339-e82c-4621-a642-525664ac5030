@import '../../../styles/variables';

.chip-select {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 5px 10px;

  & > legend {
    padding: 0;
    padding-bottom: 10px;

    // matches Input floating labels
    font-size: 0.875rem;
    font-weight: 600;
  }
}

.chip {
  border: solid 1px $grey-20;
  border-radius: 40px;
  padding: 0.3125em 0.625em;
  user-select: none;
  cursor: pointer;
  flex-shrink: 0;

  &:has(input:focus-visible) {
    outline: 5px auto Highlight;
    outline: 5px auto -webkit-focus-ring-color;
  }

  & input {
    appearance: none;
    margin: 0;
    padding: 0;
    width: 0;
    height: 0;
    position: absolute;
  }

  // caniuse :has not yet widely baseline
  &.checked,
  &:has(input:checked) {
    background: $grey-10;
    font-weight: 500;
  }
}

@keyframes errorSlide {
  from {
    opacity: 0;
    transform: translateY(-15%);
  }

  to {
    opacity: 1;
    transform: none;
  }
}

.error {
  position: absolute;
  top: 100%;
  padding-top: 2px;
  color: $red-80;
  font-size: 0.75rem;
  animation: errorSlide 200ms ease-in-out;
}
