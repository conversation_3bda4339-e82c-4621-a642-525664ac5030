import React from 'react';
import styles from './ChipSelect.module.scss';
import cn from 'classnames';
import BoldShiftText from '../../BoldShiftText/BoldShiftText';
import type { Option } from './types';

type ChipSelectProps = Pick<
  React.InputHTMLAttributes<HTMLInputElement>,
  'className' | 'name' | 'onBlur' | 'value'
> & {
  onChange?: (option: Option) => void;
  onClick?: (option: Option) => void;
  options: Option[];
  label?: string;
  error?: string;
  touched?: boolean;
};

const ChipSelect: React.FC = ({
  className,
  name,
  onChange,
  onClick,
  onBlur,
  value,
  options,
  label,
  error,
  touched,
}: ChipSelectProps) => {
  return (
    <fieldset className={cn(styles['chip-select'], className)}>
      {label && <legend className={styles['legend']}>{label}</legend>}

      {options.map((op) => (
        <label
          key={op.value}
          className={cn(styles.chip, {
            [styles.checked]: value === op.value,
          })}
          htmlFor={`${name}-${op.value}`}
          data-text={op.title}
        >
          <input
            type="radio"
            id={`${name}-${op.value}`}
            name={name}
            value={op.value}
            // use option object instead of event so number values can be set
            // instead of string to preserve backwards compatibility
            onChange={onChange && (() => onChange(op))}
            onClick={onClick && (() => onClick(op))}
            onBlur={onBlur}
            checked={value ? value === op.value : undefined}
          />
          <BoldShiftText boldWeight={500}>{op.title}</BoldShiftText>
        </label>
      ))}

      {touched && error && <div className={styles.error}>{error}</div>}
    </fieldset>
  );
};

export type { ChipSelectProps, Option };
export default ChipSelect;
