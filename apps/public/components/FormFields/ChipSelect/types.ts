type Option = {
  value: string | number;
  title: string;
};

type OptionWithLink = Option & {
  link: string;
};

type ChipSelectProps = Pick<
  React.InputHTMLAttributes<HTMLInputElement>,
  'className' | 'name' | 'onBlur' | 'value'
> & {
  onChange?: (option: Option) => void;
  onClick?: (option: Option) => void;
  options: Option[];
  label?: string;
  error?: string;
  touched?: boolean;
};

type ChipLinksProps = Pick<ChipSelectProps, 'className' | 'value'> & {
  options: OptionWithLink[];
};

export type { Option, OptionWithLink, ChipSelectProps, ChipLinksProps };
