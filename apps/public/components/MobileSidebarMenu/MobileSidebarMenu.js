import React, { memo, useEffect, useState } from 'react';
import { <PERSON><PERSON>, NumberBadge } from '../index';
import Image from 'next/image';
import styles from './MobileSidebarMenu.module.scss';
import {
  companyLinks,
  exploreLinks,
} from '../../constants/menuLinks/sidebar-links';
import cn from 'classnames';
import SaleMobileHeader from '../Header/SaleMobileHeader';
import Link from 'next/link';
import { Amp } from '../../services/amp';
import { useAlert } from '../../contexts/AlertContext';

const MobileSidebarMenu = ({
  onSideBarClose,
  open,
  onShowSignUpModal,
  onShowLoginModal,
  isAuthenticated,
  profile,
  isTalent,
  isPaidOrDelayed,
  canUpgradeExistingSubscription,
  gender,
  authLogout,
  saleExpirationTime,
  stopSale,
  showSale,
  redirectURL,
}) => {
  const [showSideBar, setShowSideBar] = useState(false);

  const { newReviewCount } = useAlert();

  const openLoginModal = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'log in',
      scope: Amp.element.scope.burger,
      type: Amp.element.type.button,
    });
    onSideBarClose();
    onShowLoginModal();
  };

  const openSignUpModal = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'sign up',
      scope: Amp.element.scope.burger,
      type: Amp.element.type.button,
    });
    onSideBarClose();
    onShowSignUpModal();
  };

  useEffect(() => {
    setShowSideBar(open);
  }, [open]);

  const logout = () => {
    onSideBarClose();
    authLogout();
  };

  const onMenuItemClick = (label) => {
    Amp.track(Amp.events.elementClicked, {
      name: label,
      scope: Amp.element.scope.burger,
      type: Amp.element.type.link,
    });
    onSideBarClose();
  };

  const onNavigateToSettings = () => {
    window.location.href = `${process.env.redirectTalentUrl}/settings`;
  };

  const onNavigateToUpgrade = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'subscribe',
      scope: Amp.element.scope.burger,
      section: Amp.element.section.navigation,
      type: Amp.element.type.button,
      upgrade_to_12: !!canUpgradeExistingSubscription,
    });

    window.location.href = `${process.env.redirectTalentUrl}/upgrade${
      canUpgradeExistingSubscription ? '-12' : ''
    }`;
  };

  return (
    <div
      className={cn(
        styles['sidebar-container'],
        showSideBar && styles['sidebar-open'],
      )}
    >
      <button className={styles['close-btn']} onClick={onSideBarClose}>
        <Image
          width={16}
          height={16}
          src={'/assets/icons/icon-close-3.svg'}
          alt="icon-close-3.svg"
        />
      </button>
      <div className={styles['sidebar-header']}>
        {!isAuthenticated ? (
          <>
            <div className={styles['logo-wrap']} onClick={onSideBarClose}>
              <Link href={'/'}>
                <Image
                  className={styles['logo-link']}
                  src={'/assets/logo/logo-3.svg'}
                  width={170}
                  height={70}
                  alt="logo-3.svg"
                />
              </Link>
            </div>
            <button onClick={openSignUpModal} className={styles['link-button']}>
              SIGN UP
            </button>
            <span onClick={openLoginModal} className={styles.link}>
              Log in
            </span>
          </>
        ) : (
          <>
            <div className={styles['rating-container']}>
              <Link
                href={`${redirectURL}${profile.profileUrl}/info`}
                className={styles.button}
              >
                <img
                  className={styles['profile-img']}
                  src={
                    profile.titlePhotoUrl ||
                    `/assets/placeholders/circle-${gender}-headshot.svg`
                  }
                  alt={''}
                />
              </Link>
              {isTalent && (
                <div className={styles.rating}>
                  <Image
                    className={styles['star']}
                    src={'/assets/icons/icon-star-2.svg'}
                    width={17}
                    height={17}
                    alt="star"
                  />
                  <span>{profile.rating.toLocaleString('en-EN')}</span>
                </div>
              )}
            </div>
            <Link
              href={`${redirectURL}${profile.profileUrl}/info`}
              className={styles['profile-name']}
            >
              {profile.fullName}
            </Link>
          </>
        )}
        {showSale && (
          <SaleMobileHeader
            saleExpirationTime={saleExpirationTime}
            stopSale={stopSale}
            className={styles['sale-header']}
            loweredOpacity
            loweredFontWeight
          />
        )}
        {!showSale &&
          isTalent &&
          (!isPaidOrDelayed || canUpgradeExistingSubscription) && (
            <div className={styles['subscribe-button-container']}>
              <Button
                onClick={onNavigateToUpgrade}
                label={
                  canUpgradeExistingSubscription
                    ? 'Upgrade your plan'
                    : 'Subscribe'
                }
                color="green-gradient"
                minWidth="220px"
                shadow={false}
              />
            </div>
          )}
        {isAuthenticated && (
          <div className={styles['edit-profile']}>
            <Link
              href={
                isTalent
                  ? `${process.env.redirectTalentUrl}/profile/${profile.id}/info`
                  : `${process.env.redirectProUrl}/director/${profile.id}/info`
              }
              className={styles.link}
            >
              edit profile
            </Link>
          </div>
        )}
      </div>
      <div className={styles.sidebar}>
        <div className={styles.container}>
          <div className={styles.title}>Explore</div>
          <div className={styles['menu-links']}>
            {exploreLinks.map(({ title, id, routerLink }) => (
              <Link
                className={cn(styles['menu-link'], {
                  [styles.hidden]: id === 'directors' && isAuthenticated,
                })}
                key={id}
                href={routerLink}
                onClick={() => onMenuItemClick(title)}
              >
                {title}
                {isAuthenticated && id === 'castingcalls' && (
                  <NumberBadge number={99} />
                )}
                {isAuthenticated && id === 'reviews' && newReviewCount > 0 && (
                  <NumberBadge number={newReviewCount} />
                )}
              </Link>
            ))}
          </div>
        </div>
        <div className={styles.container}>
          <div className={styles.title}>Company</div>
          <div className={styles['menu-links']}>
            {companyLinks.map(({ title, id, routerLink }) => (
              <Link
                className={styles['menu-link']}
                key={id}
                href={routerLink}
                onClick={() => onMenuItemClick(title)}
              >
                {title}
              </Link>
            ))}
          </div>
        </div>
        {isAuthenticated && (
          <div className={styles['action-buttons']}>
            <Button
              className={cn(
                styles['action-button'],
                styles['action-button-logout'],
              )}
              color="grey"
              label="Log Out"
              minWidth="130px"
              shadow={false}
              onClick={logout}
            />
            <Button
              className={cn(
                styles['action-button'],
                styles['action-button-settings'],
              )}
              color="grey"
              label="Settings"
              minWidth="130px"
              shadow={false}
              onClick={onNavigateToSettings}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(MobileSidebarMenu);
