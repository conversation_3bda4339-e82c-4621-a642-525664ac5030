import React from 'react';
import Button from '../Button/Button';
import styles from './HowTo.module.scss';
import Bulb from '../../public/assets/svg/bulb.svg';
import cn from 'classnames';
import type { HowTo as HowToProps } from '../../services/endpoints/articles';

const HowTo = ({ name, description, items, cta }: HowToProps) => {
  return (
    <>
      <h2 className={cn(styles.title, styles['how-to'])}>How To</h2>
      <article className={styles.content}>
        <header className={styles.header}>
          <h2 className={styles.title}>{name}</h2>
          <p>{description}</p>
        </header>
        <ol className={styles.ol}>
          {items.map((step, index) => (
            <li key={index} className={styles.li}>
              <div className={styles['step-header']}>
                <div className={styles['step-number']}>{index + 1}</div>
                <b>{step.title}</b>
              </div>
              <div>{step.description}</div>
              {step.pro_tip && (
                <div className={cn(styles.tip)}>
                  <span className={styles['pro-tip']}>
                    <Bulb width={14} className={styles.bulb} />
                    <b>Pro Tip: </b>
                  </span>
                  {step.pro_tip}
                </div>
              )}
            </li>
          ))}
        </ol>
        {cta.link && cta.text && (
          <footer className={styles.footer}>
            <Button
              label={cta.text}
              type="link"
              href={cta.link}
              minWidth={180}
              color="purple"
              lowercase
              shadow
            />
          </footer>
        )}
      </article>
    </>
  );
};

export default HowTo;
